import { use } from 'echarts'
// 主要引入 柱状图 和 饼图 和 地图
import { <PERSON><PERSON>hart, MapChart, PieChart } from 'echarts/charts'
import VChart from 'vue-echarts'
// 引入提示框，标题，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'
// 标签自动布局、全局过渡动画等特性
import { LabelLayout, UniversalTransition } from 'echarts/features'
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { Map3DChart, Scatter3DChart } from 'echarts-gl/charts'
import { Grid3DComponent } from 'echarts-gl/components'
import 'echarts-liquidfill'
import { CanvasRenderer } from 'echarts/renderers'
use([
  <PERSON><PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  MapChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  Map3DChart,
  Scatter3DChart,
  Grid3DComponent,
])

export default {
  install: (app) => {
    app.component('VChart', VChart)
  },
}
