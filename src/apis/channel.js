import { get } from '@/utils/request/alova'

/**
 * 检查 BOSS 直聘是否为登录状态
 * @param {*} data
 * @returns
 */
export const checkBossLogin = (data) => {
  return get('/api/v1/channel/login/status', data)
}

/**
 * 获取 BOSS 直聘登录二维码
 * @param {*} data
 * @returns
 */
export const getBossQrCode = (data) => {
  return get('/api/v1/channel/login/code', data)
}

/**
 * 登录 BOSS 直聘
 * @param {*} data
 * @returns
 */
export const loginBoss = (data, signal) => {
  return get('/api/v1/channel/login', data, {
    timeout: 0,
    signal,
    showMsg: false,
  })
}
