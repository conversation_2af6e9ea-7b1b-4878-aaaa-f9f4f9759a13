import { get, post } from '@/utils/request/alova'
import axios from 'axios'

/**
 * 获取职位列表
 * @param {*} data
 * @returns
 */
export const getJobList = (data) => {
  return post('/api/v1/position/page', data)
}
/**
 * 获取职位列表 打招呼使用
 * + 不关心角色，只返回当前用户发布的职位
 * @param {*} data
 * @returns
 */
export const getJobListInGreet = (data) => {
  return post('/api/v1/position/page/greet', data)
}

/**
 * 获取职位详情
 * @param {*} data
 * @returns
 */
export const getJobDetail = (data) => {
  return get('/api/v1/position/get', data)
}

/**
 * 删除职位
 * @param {*} data
 * @returns
 */
export const deleteJob = (data) => {
  return post('/api/v1/position/del', data)
}

/**
 * 关闭职位
 * @param {*} data
 * @returns
 */
export const closeJob = (data) => {
  return post('/api/v1/position/shutdown', data)
}

/**
 * 发布(打开)职位
 * @param {*} data
 * @returns
 */
export const openJob = (data) => {
  return post('/api/v1/position/release', data)
}

/**
 * 保存职位
 * @param {*} data
 * @returns
 */
export const saveJob = (data) => {
  return post('/api/v1/position/save', data)
}

/**
 * 发布职位
 * @param {*} data
 * @returns
 */
export const publishJob = (data) => {
  return post('/api/v1/position/release', data)
}

/**
 * 获取可以选择的职位名称
 * @param {*} data
 * @returns
 */
export const getJobNameOptions = (data) => {
  return get('/api/v1/position/dict/get', data)
}

/**
 * 生成职位描述、职位关键词
 * @param {*} data
 * @returns
 */
export const generateJobDescAndKeywords = (data) => {
  const service = axios.create({
    baseURL: import.meta.env.VITE_FAST_GPT_URL,
  })

  return service.post(
    `/api/v1/chat/completions`,
    {
      variables: {
        ...data,
      },
      stream: false,
      detail: false,
      messages: [
        {
          role: 'user',
          content: '123',
        },
      ],
    },
    {
      headers: {
        Authorization: `Bearer ${import.meta.env.VITE_FAST_GPT_API_KEY}`,
      },
    },
  )
}

/**
 * 获取租户的知识库 id
 * @param {*} data
 * @returns
 */
export const getTenantKnowledgeBase = (data) => {
  return get('/api/v1/enterprise/getDatasetId', data)
}

/**
 * 获取工作地点列表
 * @param {*} data
 * @returns
 */
export const getWorkLocationList = (data) => {
  return get('/api/v1/location/list', data)
}

/**
 * 添加工作地点
 * @param {*} data
 * @returns
 */
export const addWorkLocation = (data) => {
  return post('/api/v1/location/add', data)
}

/**
 * 获取工作城市下拉
 * @param {*} data
 * @returns
 */
export const getWorkCityOptions = (data) => {
  return get('/api/v1/resume/info/secondLevelDropdown', data)
}

/**
 * 搜索办公大楼
 * @param {*} data
 * @returns
 */
export const searchOfficeBuilding = (data) => {
  return get('/api/v1/common/map/query', data)
}

/**
 * 删除工作地点
 * @param {*} data
 * @returns
 */
export const deleteWorkLocation = (data) => {
  return post('/api/v1/location/del', data)
}

/**
 * 编辑工作地点
 * @param {*} data
 * @returns
 */
export const editWorkLocation = (data) => {
  return post('/api/v1/location/update', data)
}

/**
 * 获取部门列表
 * @param {*} data
 * @returns
 */
export const getDeptList = (data) => {
  return get('/api/v1/common/organization/tree', data)
}

/**
 * 获取已发布的职位列表
 * @param {*} data
 * @returns
 */
export const getPublishedJobList = (data) => {
  return get('/api/v1/position/release/get', data)
}

/**
 * 获取有关联简历的职位列表
 * @param {*} data
 * @returns
 */
export const getRelatedJobList = (data) => {
  return get('/api/v1/position/related/resume', data)
}
