import { get, post } from '@/utils/request/alova'

/**
 * AI寻人
 * @param {*} data
 * @returns
 */
export const addMatchGreet = (data) => {
  return post('/api/v1/greet/match/add', data)
}

export const getGreetPositionList = (data) => {
  return get('/api/v1/greet/position/list', data)
}
/**
 * 获取匹配和打招呼数量
 * @param {*} data
 * @returns
 */
export const getGreetCount = (data) => {
  return get('/api/v1/greet/count', data)
}

/**
 * 获取打招呼匹配列表
 * @param {*} data
 * @returns
 */
export const getGreetMatchList = (data, config) => {
  return post('/api/v1/greet/match/pagelist', data, config)
}
/**
 * 获取打招呼列表
 * @param {*} data
 * @returns
 */
export const getGreetList = (data) => {
  return post('/api/v1/greet/pagelist', data)
}

/**
 * 获取候选人列表
 * @param {*} data
 * @returns
 */
export const getCandidateList = (data) => {
  return post('/api/v1/greet/match/candidate/pagelist', data)
}

/**
 * 淘汰候选人
 * + 可以一键淘汰 不需要传递候选人ids
 * + 传递ids
 * @param {*} data
 * @returns
 */
export const disuseCandidate = (data) => {
  return post('/api/v1/greet/disuse', data)
}

/**
 * 对候选人打招呼
 * + 只对勾选的候选人打招呼 其他未勾选的人自动淘汰
 * @param {*} data
 * @returns
 */
export const greetCandidate = (data) => {
  return post('/api/v1/greet', data)
}

/**
 * 判断当前账号是否可以AI寻人
 * @param {*} data
 * @returns
 */
export const getCanAiMatch = (data) => {
  return get('/api/v1/greet/canMatch', data)
}

/**
 * 手动绑定人才
 * @param {*} data
 * @returns
 */
export const bindTalent = (data) => {
  return post('/api/v1/greet/bind', data)
}

/**
 * 解除绑定人才
 * @param {*} data
 * @returns
 */
export const unbindTalent = (bid, data) => {
  return post(`/api/v1/greet/unbind/${bid}`, data)
}
