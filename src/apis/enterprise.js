import { get, post, upload } from '@/utils/request/alova'

/**
 *  下载操作文件
 */
export const downloadOperationFile = (data) => {
  return get('/api/v1/common/download', data, {
    responseType: 'blob',
  })
}

/**
 * 获取文件/文件夹列表
 * @param {*} data
 * @returns
 */
export const getFileTree = (data) => {
  return get('/api/v1/enterprise/fileTree', data)
}

/**
 * 根据名字查询文件/文件夹
 * @param {*} data
 * @returns
 */
export const searchFile = (data) => {
  return get('/api/v1/enterprise/fileList', data)
}

/**
 * 创建文件夹
 * @param {*} data
 * @returns
 */
export const createFolder = (data) => {
  return post('/api/v1/enterprise/newFolder', data)
}

/**
 * 上传文件
 * @param {*} data
 * @returns
 */
export const uploadFile = (data) => {
  return upload('/api/v1/enterprise/uploadFile', data)
}

/**
 * 获取文件下载地址
 * @param {*} data
 * @returns
 */
export const getFileUrl = (data) => {
  return get(`/api/v1/enterprise/getDownloadUrl`, data)
}

/**
 * 删除文件
 * @param {*} fileBid
 * @param {*} data
 * @returns
 */
export const deleteFile = (fileBid, data) => {
  return post(`/api/v1/enterprise/removeFile/${fileBid}`, data)
}

/**
 * 删除文件夹
 * @param {*} folderBid
 * @param {*} data
 * @returns
 */
export const deleteFolder = (folderBid, data) => {
  return post(`/api/v1/enterprise/removeFolder/${folderBid}`, data)
}

/**
 * 批量下载
 * @param {*} data
 * @returns
 */
export const batchDownload = (data) => {
  return post('/api/v1/enterprise/downloadZip', data, {
    responseType: 'blob',
  })
}

/**
 * 批量删除
 * @param {*} data
 * @returns
 */
export const batchDelete = (data) => {
  return post('/api/v1/enterprise/removeFolderBatch', data)
}
