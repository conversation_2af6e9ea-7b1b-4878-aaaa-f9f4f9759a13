import { apiBaseUrl, spaceId } from '@/utils/config.js'
import { get, upload } from '@/utils/request/alova'

/**
 * 上传文件地址
 */
export const uploadURL = apiBaseUrl + '/api/v1/file/uploads'

/**
 * 退出登录
 * @param {*} data
 * @returns
 */
export const logout = (data) => {
  return get(`${apiBaseUrl}/openapi/v1/user/logout`, data)
}

/**
 * 获取统一认证平台的用户信息 如果存在spaceId则会返回用户的角色信息
 * @param {*} data
 * @returns
 */
export const getOpenUserInfo = (data) => {
  return get(
    `${apiBaseUrl}/openapi/v1/user/current_user?spaceId=${spaceId}`,
    data,
    {
      cacheFor: 10 * 8000,
    },
  )
}

/**
 * 获取用户角色
 * @param {*} data
 * @returns
 */
export const getOpenUserRoles = (data) => {
  return get(
    `${apiBaseUrl}/openapi/v1/user/current_roles?spaceId=${spaceId}`,
    data,
    {
      cacheFor: 10 * 8000,
    },
  )
}

/**
 * 获取用户菜单
 * @param {*} data
 * @returns
 */
export const getOpenUserMenus = (data) => {
  return get(
    `${apiBaseUrl}/openapi/v1/user/current_menus?spaceId=${spaceId}`,
    data,
    {
      cacheFor: 10 * 8000,
    },
  )
}

/**
 * 获取所有用户信息
 * @returns
 */
export const getAllUserInfo = () =>
  Promise.all([getOpenUserInfo(), getOpenUserRoles(), getOpenUserMenus()])

/**
 * 根据字典类型获取字典数据
 * @param {String} types 	字符串，字典类型，多个使用,分割
 * @returns
 */
export const getDictByKey = (types) => {
  return get(`${apiBaseUrl}/openapi/v1/user/query_dict?spaceId=${spaceId}`, {
    types: types,
  })
}

/**
 * 上传文件
 * @param {*} data
 * @returns
 */
export const uploadFile = (data) => {
  return upload(uploadURL, data)
}
