import { get } from '@/utils/request/alova'

/**
 * 获取几个统计数据
 * @param {*} data
 * @returns
 */
export function getStatistics(data) {
  return get('/api/home/<USER>', data)
}

/**
 * 获取职位发布状态分析
 * @param {*} data
 * @returns
 */
export function getJobStatus(data) {
  return get('/api/home/<USER>', data)
}

/**
 * 获取简历投递趋势
 * @param {*} data
 * @returns
 */
export function getResumeTrend(data) {
  return get('/api/home/<USER>', data)
}

/**
 * 获取沟通与简历获取分布
 * @param {*} data
 * @returns
 */
export function getCommunication(data) {
  return get('/api/home/<USER>', data)
}
