import { get } from '@/utils/request/alova'

/**
 * 查询微信账号列表
 * @returns
 */
export function getWeChatList(data) {
  return get('/api/v1/wechat/account/list', data)
}
/**
 * 修改人才微信号
 * @returns
 */
export function updateWeChatAccount(data) {
  return get('/api/v1/wechat/account/updateWechat', data)
}
/**
 * 查询企业微信账号列表
 * @returns
 */
export function getWeChatWorkList(data) {
  return get('/api/v1/wxwork/account/list', data)
}
/**
 * 查询企业微信账号详情
 * @returns
 */
export function getWeChatWorkDetail(data) {
  return get('/api/v1/wxwork/account/detail', data)
}

/**
 * 查询企业微信账号列表
 * @returns
 */
export function getWeChatWorkListByBid(data) {
  return get('/api/v1/wxwork/account/listByWxwork', data)
}
