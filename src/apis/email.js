import { get, post } from '@/utils/request/alova'

/**
 * 获取邮箱列表
 * @param {*} data
 * @returns
 */
export const getEmailList = (data) => {
  return post('/api/v1/email/page', data)
}

/**
 * 添加邮箱
 * @param {*} data
 * @returns
 */
export const addEmail = (data) => {
  return post('/api/v1/email/add', data)
}

/**
 * 编辑邮箱
 * @param {*} data
 * @returns
 */
export const updateEmail = (data) => {
  return post('/api/v1/email/update', data)
}

/**
 * 校验邮箱
 * @param {*} data
 * @returns
 */
export const testEmail = (data) => {
  return get('/api/v1/email/check', data)
}

/**
 * 删除邮箱
 * @param {*} data
 * @returns
 */
export const deleteEmail = (data) => {
  return get('/api/v1/email/del', data)
}
