<template>
  <header class="header">
    <img
      alt=""
      src="@/assets/images/logo.png" />
    <div class="action_btns">
      <!-- <ElButton
        class="mr-[20px]"
        type="primary"
        @click="inLargeScreen">
        进入大屏
      </ElButton> -->
      <div class="action_btn">
        <img
          v-show="avatar"
          class="rounded-full"
          :src="avatar"
          alt="" />
        <img
          v-show="!avatar"
          class="rounded-full"
          alt=""
          src="@/assets/images/user.png" />
      </div>
      <span class="welcome_text">{{ username || '' }}</span>
      <div
        class="action_btn"
        @click="exit">
        <i class="iconfont icon-quit text-[16px] text-[#4E5969]" />
      </div>
    </div>
  </header>
</template>

<script setup>
  // import { useLargeScreenStore } from '@/stores/large-screen.js'
  import useUserStore from '@/stores/user'

  // const largeScreenStore = useLargeScreenStore()
  // const inLargeScreen = () => {
  //   largeScreenStore.setShow()
  // }

  const userStore = useUserStore()

  const avatar = computed(() => {
    return userStore.userInfo?.avatar
  })
  const username = computed(() => {
    return userStore.userInfo?.fullName
  })

  const exit = () => {
    ElMessageBox({
      title: '退出',
      type: 'warning',
      message: '确定要退出该项目吗?',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          try {
            await userStore.logout()
            ElMessage.success('退出成功,请重新登录')
          } catch (err) {
            console.log(err)
          } finally {
            instance.confirmButtonLoading = false
            done()
          }
        } else {
          done()
        }
      },
    })
  }
</script>

<style lang="scss" scoped>
  .header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    width: 100%;
    min-width: 1393px;
    height: 60px;
    padding-right: 20px;
    padding-left: 24px;
    border: 1px solid rgb(0 0 0 / 5%);
    background-color: #fff;

    .action_btns {
      display: flex;
      position: absolute;
      right: 0;
      align-items: center;
      height: 80px;
      padding: 0 20px;

      .action_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: 1px solid #f2f3f5;
        border-radius: 50%;
        cursor: pointer;

        &:first-child {
          border: none;
          background: rgb(45 91 255 / 5%);
          cursor: auto;
        }
      }

      .welcome_text {
        margin-right: 28px;
        margin-left: 12px;
        color: #3d3d3d;
        font-size: 14px;
      }
    }
  }
</style>
