<template>
  <ElSubMenu
    v-if="!isEmpty(item.children)"
    :index="item.key">
    <template #title>
      <i
        class="icon"
        :class="item.icon"></i>
      <span>{{ item.label }}</span>
    </template>
    <MenuItem
      v-for="child in item.children"
      :key="child.id"
      :item="child" />
  </ElSubMenu>

  <!-- 外链 -->
  <ElMenuItem
    v-else-if="item.meta?.isLink && !item.meta?.isIframe"
    :index="item.key">
    <div
      class="flex h-full w-full items-center overflow-hidden"
      @click.stop="handleExternalLinkClick(item)">
      <i
        class="icon"
        :class="item.icon"></i>
      <span class="title">{{ item.label }}</span>
    </div>
  </ElMenuItem>

  <!-- 内嵌iframe -->
  <ElMenuItem
    v-else-if="item.meta?.isLink && item.meta?.isIframe"
    :index="`iframe?src=${item.key}`">
    <i
      class="icon"
      :class="item.icon"></i>
    <span class="title">{{ item.label }}</span>
  </ElMenuItem>

  <ElMenuItem
    v-else
    :index="item.key">
    <i
      class="icon"
      :class="item.icon"></i>
    <span class="title">{{ item.label }}</span>
  </ElMenuItem>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'

  defineProps({
    item: {
      type: Object,
      required: true,
    },
  })

  function handleExternalLinkClick(item) {
    window.open(item.key, '_blank')
  }
</script>

<style lang="scss" scoped>
  .icon {
    margin-right: 8px;
    text-align: center;
    vertical-align: middle;
  }

  .title {
    height: 40px;
    font-weight: 500;
    line-height: 40px;
  }
</style>
