<template>
  <ElAutocomplete
    v-model="modelValue"
    :fetch-suggestions="querySearch"
    :trigger-on-focus="false"
    :clearable="true"
    placeholder="请输入内容" />
</template>

<script setup>
  import { get } from '@/utils/request/alova'

  const modelValue = defineModel({
    type: String,
    default: '',
  })

  function querySearch(queryString, cb) {
    get('/api/v1/user/keyword', {
      keyword: queryString,
    }).then((data) => {
      console.log('data', data)
      cb(
        data.map((item) => {
          return {
            value: item,
          }
        }),
      )
    })
  }
</script>

<style lang="scss" scoped></style>
