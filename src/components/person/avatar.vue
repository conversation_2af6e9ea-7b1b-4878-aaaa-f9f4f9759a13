<template>
  <div
    class="avatar"
    :style="{
      width: `${props.size}px`,
      height: `${props.size}px`,
    }">
    <img
      :src="avatarImg"
      alt="" />
  </div>
</template>

<script setup>
  import female from '@/assets/images/avatar-female.png'
  import male from '@/assets/images/avatar-male.png'
  import unkonw from '@/assets/images/unkonw.png'

  const props = defineProps({
    avatarUrl: {
      type: String,
      default: '',
    },
    gender: {
      type: Number,
      default: -1,
    },
    size: {
      type: Number,
      default: 40,
    },
  })

  // 根据人员头像或者性别返回头像
  const avatarImg = computed(() => {
    if (/^(?:https?:)/.test(props.avatarUrl)) return props.avatarUrl
    // 0男 1 女
    return props.gender === 0 ? male : props.gender === 1 ? female : unkonw
  })
</script>

<style lang="scss" scoped>
  .avatar {
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
</style>
