<template>
  <img
    :style="{
      width: `${props.size}px`,
      height: `${props.size}px`,
    }"
    class="source"
    :src="img"
    alt="" />
</template>
<script setup>
  import boss from '@/assets/images/resume/talents/icon_boss.png'
  import manual from '@/assets/images/resume/talents/icon_manual.png'

  const props = defineProps({
    source: {
      type: String,
      default: '',
    },
    size: {
      type: Number,
      default: 24,
    },
  })

  const img = computed(() => {
    if (props.source === 'BOSS直聘') return boss
    if (props.source === '手动上传') return manual
    return ''
  })
</script>

<style lang="scss" scoped>
  .source {
    display: block;
    overflow: hidden;
    object-fit: cover;
    border-radius: 50%;
  }
</style>
