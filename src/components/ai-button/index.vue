<template>
  <button
    type="button"
    class="ai_button"
    :disabled="disabled"
    :class="{ 'ai_button-loading': loading }"
    @click="handleClick">
    <ElIcon
      v-if="loading"
      class="loading_icon">
      <Loading />
    </ElIcon>
    <i
      v-if="!loading && icon"
      class="iconfont"
      :class="icon" />
    <slot>
      {{ text }}
    </slot>
  </button>
</template>

<script setup>
  import { Loading } from '@element-plus/icons-vue'

  const { icon, text, disabled, loading, size } = defineProps({
    icon: {
      type: String,
      default: 'icon-Aisc',
    },
    text: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'default',
    },
  })

  const emit = defineEmits(['click'])

  // 按钮大小
  const buttonSize = computed(() => {
    return {
      default: {
        height: '32px',
        padding: '8px 15px',
      },
      small: {
        height: '24px',
        padding: '5px 11px',
      },
    }[size]
  })

  function handleClick() {
    if (loading) return
    if (disabled) return

    emit('click')
  }
</script>

<style lang="scss" scoped>
  .ai_button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: v-bind('buttonSize.height');
    padding: v-bind('buttonSize.padding');
    border: none;
    border-radius: 4px;
    outline: none;
    background: linear-gradient(42deg, #1749ff 0%, #00e7ff 100%);
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 85, 255, 0.3);
      opacity: 0.8;
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 1px 3px rgba(0, 85, 255, 0.2);
      opacity: 0.9;
    }

    &:disabled {
      transform: none;
      box-shadow: none;
      cursor: not-allowed;
      opacity: 0.5;
    }
    &.ai_button-loading {
      cursor: default;
      opacity: 0.5;
    }

    & > i {
      margin-right: 4px;
      font-size: 12px;
    }

    & + & {
      margin-left: 12px;
    }

    .loading_icon {
      animation: loading 2s linear infinite;
    }
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
