<template>
  <ElSelect
    v-if="modelType === 'positionName'"
    v-model="positionName"
    placeholder="请输入职位名称"
    :disabled="disabled"
    :filterable="true"
    :remote="true"
    :clearable="true"
    :remote-method="getPositionNameOptions"
    @clear="() => (positionBid = '')">
    <ElOption
      v-for="item in positionNameOptions"
      :key="item.positionName"
      :label="item.positionName"
      :value="item.positionName" />
  </ElSelect>
  <ElSelect
    v-else
    v-model="positionBid"
    placeholder="请输入职位名称"
    :disabled="disabled"
    :filterable="true"
    :remote="true"
    :clearable="true"
    :remote-method="getPositionNameOptions"
    @change="
      (positionBid) =>
        (positionName =
          positionNameOptions.find((item) => item.bid === positionBid)?.positionName || '')
    ">
    <ElOption
      v-for="item in positionNameOptions"
      :key="item.positionName"
      :label="item.positionName"
      :value="item.bid" />
  </ElSelect>
</template>

<script setup>
  import { getRelatedJobList } from '@/apis/job'

  const positionName = defineModel('positionName', {
    type: String,
    default: '',
  })

  const positionBid = defineModel('positionBid', {
    type: String,
    default: '',
  })

  const { disabled, modelType } = defineProps({
    modelType: {
      type: String,
      default: 'positionName',
      validator: (value) => ['positionName', 'positionBid'].includes(value),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  })
  const positionNameOptions = ref([])
  function getPositionNameOptions(query) {
    if (!query) {
      positionNameOptions.value = []
      return
    }
    getRelatedJobList({
      positionName: query,
    }).then((data) => {
      positionNameOptions.value = data
    })
  }

  defineExpose({
    getPositionNameOptions,
  })
</script>

<style lang="scss" scoped></style>
