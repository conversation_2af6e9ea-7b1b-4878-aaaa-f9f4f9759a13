<template>
  <div class="label_value">
    <label
      :style="{
        width: typeof labelWidth === 'number' ? `${labelWidth}px` : labelWidth,
      }">
      {{ label }}
    </label>
    <div class="label_value_container">
      <slot />
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    label: {
      type: String,
      default: '',
    },
    labelWidth: {
      type: [String, Number],
      default: 100,
    },
  })
  const { label, labelWidth } = toRefs(props)
</script>

<style lang="scss" scoped>
  .label_value {
    display: flex;
    align-items: flex-start;
    width: 100%;
    min-height: 50px;
    padding-bottom: 18px;

    label {
      display: flex;
      align-items: center;
      min-height: 32px;
      padding-right: 12px;
      color: #303133;
      font-size: 14px;
    }

    &_container {
      display: flex;
      flex: 1;
      align-items: center;
      min-height: 32px;
      color: #606266;
      font-size: 14px;
    }
  }
</style>
