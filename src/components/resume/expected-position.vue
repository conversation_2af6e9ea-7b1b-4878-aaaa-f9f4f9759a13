<template>
  <div class="info_item">
    <h1>期望职位</h1>
    <div class="flex justify-between">
      <ElSpace
        class="text-[14px]/[22px] text-[#606266]"
        :size="6"
        :spacer="spacer">
        <span>{{ data.positionName }}</span>
        <span>{{ data.locationName }}</span>
        <span>{{ data.industryDesc }}</span>
      </ElSpace>
      <span class="text-[14px]/[22px] text-[#0055FF]">{{ data.salaryDesc }}</span>
    </div>
  </div>
</template>

<script setup>
  import { isArray, isObject } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacer = h('span', '·')

  const data = computed(() => {
    const expect = info.showExpectPosition
    if (isArray(expect)) {
      return expect[0] || {}
    } else if (isObject(expect)) {
      return expect
    } else {
      return {}
    }
  })
</script>

<style lang="scss" scoped>
  .info_item {
    h1 {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
