<template>
  <div class="info_item">
    <h1 class="item_title">所获荣誉</h1>
    <ElSpace
      class="w-full"
      :fill="true"
      :size="25"
      direction="vertical"
      :spacer="spacerDivider">
      <ElSpace v-if="!isEmpty(data)">
        <ElTag
          v-for="tagItem in data"
          :key="tagItem.honorName"
          type="info">
          {{ tagItem.honorName }}
        </ElTag>
      </ElSpace>
    </ElSpace>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacerDivider = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const data = computed(() => {
    return info?.geekHonorList || []
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
