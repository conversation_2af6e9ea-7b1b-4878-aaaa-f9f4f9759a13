<template>
  <div class="flex justify-between">
    <div>
      <h1 class="mb-[8px] text-[30px]/[38px] font-bold">{{ data.name }}</h1>
      <span class="text-[20px]/[28px] text-[#303133]">{{ data.applyStatusContent }}</span>
    </div>
    <PersonAvatar
      :size="74"
      :avatar-url="data.tiny"
      :gender="data.gender" />
  </div>
</template>

<script setup>
  import { PersonAvatar } from '../index'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const data = computed(() => {
    return info.geekBaseInfo || {}
  })
</script>

<style lang="scss" scoped></style>
