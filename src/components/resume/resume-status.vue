<template>
  <div>
    <i
      v-if="active"
      class="resume active iconfont icon-Obtained"></i>
    <i
      v-else
      class="resume not iconfont icon-Notobtained"></i>
  </div>
</template>

<script setup>
  const { size } = defineProps({
    active: {
      type: Boolean,
      default: false,
    },
    size: {
      type: Number,
      default: 100,
    },
  })

  const fontSize = computed(() => {
    return size + 'px'
  })
</script>

<style lang="scss" scoped>
  div {
    & > i {
      font-size: v-bind(fontSize);
      &.active {
        color: #b3e09c;
      }
      &.not {
        color: #e6e8eb;
      }
    }
  }
</style>
