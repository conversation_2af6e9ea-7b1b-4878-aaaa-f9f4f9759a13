<template>
  <div class="info_item">
    <h1 class="item_title">教育经历</h1>
    <ElSpace
      class="w-full"
      :fill="true"
      :size="25"
      direction="vertical"
      :spacer="spacerDivider">
      <ElSpace
        v-for="eduItem in data"
        :key="eduItem.school"
        class="w-full"
        :fill="true"
        :size="25"
        direction="vertical">
        <div class="flex flex-col gap-[4px]">
          <div class="flex items-center justify-between">
            <ElSpace :size="12">
              <h1
                v-if="!isEmpty(eduItem.school)"
                class="text-[18px]/[26px] text-[#606266]">
                {{ eduItem.school }}
              </h1>
              <ElSpace
                v-if="!isEmpty(eduItem.tags)"
                :size="8">
                <ElTag
                  v-for="tagItem in eduItem.tags"
                  :key="tagItem"
                  type="primary">
                  {{ tagItem }}
                </ElTag>
              </ElSpace>
            </ElSpace>
            <span
              v-if="!isEmpty(eduItem.startYearStr) && !isEmpty(eduItem.endYearStr)"
              class="text-[14px]/[22px] text-[#909399]">
              {{ eduItem.startYearStr + '-' + eduItem.endYearStr }}
            </span>
          </div>
          <ElSpace
            class="text-[16px]/[28px] text-[#606266]"
            :size="6"
            :spacer="spacer">
            <span v-if="!isEmpty(eduItem.major)">{{ eduItem.major }}</span>
            <span v-if="!isEmpty(eduItem.degreeName)">{{ eduItem.degreeName }}</span>
          </ElSpace>
        </div>
        <ElSpace
          class="w-full"
          :fill="true"
          :size="16"
          direction="vertical">
          <!-- 专业排名 -->
          <div v-if="!isEmpty(eduItem.majorRankingDesc)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#606266]">专业排名：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="eduItem.majorRankingDesc"></span>
          </div>
          <!-- 主修课程 -->
          <div v-if="!isEmpty(eduItem.courseDesc)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#606266]">主修课程：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="eduItem.courseDesc"></span>
          </div>
          <!-- 在校经历 -->
          <div v-if="!isEmpty(eduItem.eduDescription)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#606266]">在校经历：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="eduItem.eduDescription"></span>
          </div>
          <!-- 论文 -->
          <div v-if="!isEmpty(eduItem.thesisTitle)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#606266]">毕设/论文：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="eduItem.thesisTitle"></span>
          </div>
          <!-- 描述 -->
          <div v-if="!isEmpty(eduItem.thesisDesc)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#606266]">描述：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="eduItem.thesisDesc"></span>
          </div>
        </ElSpace>
      </ElSpace>
    </ElSpace>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacer = h('span', '·')
  const spacerDivider = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const data = computed(() => {
    return info?.geekEduExpList || []
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
