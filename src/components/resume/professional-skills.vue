<template>
  <div class="info_item">
    <h1 class="item_title">专业技能</h1>
    <p
      v-if="!isEmpty(data)"
      class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
      v-html="data"></p>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const data = computed(() => {
    return info?.professionalSkill || ''
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
