<template>
  <div>
    <ElSpace
      class="mb-[18px] text-[14px]/[22px] text-[#606266]"
      :size="25">
      <ElSpace v-if="!isEmpty(data.workYearDesc)">
        <i class="iconfont icon-position"></i>
        <span>{{ data.workYearDesc }}</span>
      </ElSpace>
      <ElSpace v-if="!isEmpty(data.degreeCategory)">
        <i class="iconfont icon-educational"></i>
        <span>{{ data.degreeCategory }}</span>
      </ElSpace>
      <ElSpace v-if="!isEmpty(data.ageDesc)">
        <i class="iconfont icon-age"></i>
        <span>{{ data.ageDesc }}</span>
      </ElSpace>
    </ElSpace>
    <p
      v-if="!isEmpty(data.userDescription)"
      class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]">
      {{ data.userDescription }}
    </p>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const data = computed(() => {
    return info.geekBaseInfo || {}
  })
</script>

<style lang="scss" scoped></style>
