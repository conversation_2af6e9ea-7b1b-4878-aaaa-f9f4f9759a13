<template>
  <ElSpace
    :style="{
      width: '100%',
    }"
    :fill="true"
    direction="vertical"
    :size="25"
    :spacer="spacer">
    <!-- 姓名头像 -->
    <BaseInfo
      v-if="!isEmpty(detail.geekBaseInfo)"
      :info="detail" />
    <!-- 基本信息简介 -->
    <BaseDesc
      v-if="!isEmpty(detail.geekBaseInfo)"
      :info="detail" />
    <!-- 期望职位 -->
    <ExpectedPosition
      v-if="!isEmpty(info.showExpectPosition)"
      :info="info" />
    <!-- 工作经历 -->
    <WorkExperience
      v-if="!isEmpty(detail.geekWorkExpList)"
      :info="detail" />
    <!-- 项目经历 -->
    <ProjectExperience
      v-if="!isEmpty(detail.geekProjExpList)"
      :info="detail" />
    <!-- 教育经历 -->
    <EduExperience
      v-if="!isEmpty(detail.geekEduExpList)"
      :info="detail" />
    <!-- 社团/组织经历 -->
    <ClubExperience
      v-if="!isEmpty(detail.geekClubExpList)"
      :info="detail" />
    <!-- 所获荣誉 -->
    <HonorsReceived
      v-if="!isEmpty(detail.geekHonorList)"
      :info="detail" />
    <!-- 专业技能 -->
    <ProfessionalSkills
      v-if="!isEmpty(detail.professionalSkill)"
      :info="detail" />
    <!-- 图片作品 -->
    <ImageWork
      v-if="!isEmpty(detail.geekDesignWorksGather)"
      :info="detail" />
  </ElSpace>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import BaseDesc from './base-desc.vue'
  import BaseInfo from './base-info.vue'
  import ClubExperience from './club-experience.vue'
  import EduExperience from './edu-experience.vue'
  import ExpectedPosition from './expected-position.vue'
  import HonorsReceived from './honors-received.vue'
  import ImageWork from './image-work.vue'
  import ProfessionalSkills from './professional-skills.vue'
  import ProjectExperience from './project-experience.vue'
  import WorkExperience from './work-experience.vue'

  const spacer = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const props = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  // 简历详情
  const detail = computed(() => {
    return props.info?.geekDetail || {}
  })
</script>

<style lang="scss" scoped></style>
