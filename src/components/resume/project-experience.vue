<template>
  <div class="info_item">
    <h1 class="item_title">项目经历</h1>
    <ElSpace
      class="w-full"
      :fill="true"
      :size="25"
      direction="vertical"
      :spacer="spacerDivider">
      <ElSpace
        v-for="pItem in data"
        :key="pItem.projectId"
        class="w-full"
        :fill="true"
        :size="25"
        direction="vertical">
        <div class="flex flex-col gap-[4px]">
          <div class="flex items-center justify-between">
            <h1
              v-if="!isEmpty(pItem.name)"
              class="text-[18px]/[26px] text-[#606266]">
              {{ pItem.name }}
            </h1>
            <span
              v-if="!isEmpty(pItem.startYearMonStr) && !isEmpty(pItem.startYearMonStr)"
              class="text-[14px]/[22px] text-[#909399]">
              {{ pItem.startYearMonStr + '-' + pItem.endYearMonStr }}
            </span>
          </div>
          <ElSpace
            v-if="!isEmpty(pItem.roleName)"
            class="text-[16px]/[28px] text-[#606266]"
            :size="6"
            :spacer="spacer">
            <span v-html="pItem.roleName"></span>
          </ElSpace>
        </div>
        <ElSpace
          class="w-full"
          :fill="true"
          :size="16"
          direction="vertical">
          <!-- 项目简洁 -->
          <div v-if="!isEmpty(pItem.description)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#303133]">内容：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="pItem.description"></span>
          </div>
          <!-- 业绩 -->
          <div v-if="!isEmpty(pItem.performance)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#303133]">业绩：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="pItem.performance"></span>
          </div>
          <!-- 项目URL -->
          <div v-if="!isEmpty(pItem.url)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#303133]">项目链接：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="pItem.url"></span>
          </div>
        </ElSpace>
      </ElSpace>
    </ElSpace>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacer = h('span', '·')
  const spacerDivider = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const data = computed(() => {
    return info?.geekProjExpList || []
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
