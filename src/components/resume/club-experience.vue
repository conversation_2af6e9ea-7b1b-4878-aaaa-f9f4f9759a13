<template>
  <div class="info_item">
    <h1 class="item_title">社团/组织经历</h1>
    <ElSpace
      class="w-full"
      :fill="true"
      :size="25"
      direction="vertical"
      :spacer="spacerDivider">
      <ElSpace
        v-for="clubItem in data"
        :key="clubItem.name"
        class="w-full"
        :fill="true"
        :size="25"
        direction="vertical">
        <div class="flex flex-col gap-[4px]">
          <div class="flex items-center justify-between">
            <h1
              v-if="!isEmpty(clubItem.name)"
              class="text-[18px]/[26px] text-[#606266]">
              {{ clubItem.name }}
            </h1>
            <span
              v-if="!isEmpty(clubItem.startDateStr) && !isEmpty(clubItem.endDateStr)"
              class="text-[14px]/[22px] text-[#909399]">
              {{ clubItem.startDateStr + '-' + clubItem.endDateStr }}
            </span>
          </div>
          <ElSpace
            class="text-[16px]/[28px] text-[#606266]"
            :size="6"
            :spacer="spacer">
            <span v-if="!isEmpty(clubItem.roleName)">{{ clubItem.roleName }}</span>
          </ElSpace>
        </div>
        <ElSpace
          class="w-full"
          :fill="true"
          :size="16"
          direction="vertical">
          <div v-if="!isEmpty(clubItem.desc)">
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="clubItem.desc"></span>
          </div>
        </ElSpace>
      </ElSpace>
    </ElSpace>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacer = h('span', '·')
  const spacerDivider = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const data = computed(() => {
    return info?.geekClubExpList || []
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
