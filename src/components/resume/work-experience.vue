<template>
  <div class="info_item">
    <h1 class="item_title">工作经历</h1>
    <ElSpace
      class="w-full"
      :fill="true"
      :size="25"
      direction="vertical"
      :spacer="spacerDivider">
      <ElSpace
        v-for="workItem in data"
        :key="workItem.workId"
        class="w-full"
        :fill="true"
        :size="25"
        direction="vertical">
        <div class="flex flex-col gap-[4px]">
          <div class="flex items-center justify-between">
            <h1
              v-if="!isEmpty(workItem.company)"
              class="text-[18px]/[26px] text-[#606266]">
              {{ workItem.company }}
            </h1>
            <span
              v-if="!isEmpty(workItem.startYearMonStr) && !isEmpty(workItem.endYearMonStr)"
              class="text-[14px]/[22px] text-[#909399]">
              {{ workItem.startYearMonStr + '-' + workItem.endYearMonStr }}
            </span>
          </div>
          <ElSpace
            class="text-[16px]/[28px] text-[#606266]"
            :size="6"
            :spacer="spacer">
            <span v-if="!isEmpty(workItem.department)">{{ workItem.department }}</span>
            <span v-if="!isEmpty(workItem.positionName)">{{ workItem.positionName }}</span>
          </ElSpace>
        </div>
        <ElSpace
          class="w-full"
          :fill="true"
          :size="16"
          direction="vertical">
          <!-- 内容 -->
          <div v-if="!isEmpty(workItem.responsibility)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#303133]">内容：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="workItem.responsibility"></span>
          </div>
          <!-- 业绩 -->
          <div v-if="!isEmpty(workItem.workPerformance)">
            <span class="mb-[8px] text-[14px]/[22px] font-bold text-[#303133]">业绩：</span>
            <span
              class="text-[14px]/[22px] whitespace-pre-line! text-[#606266]"
              v-html="workItem.workPerformance"></span>
          </div>
          <!-- 标签 -->
          <ElSpace v-if="!isEmpty(workItem.workEmphasisList)">
            <ElTag
              v-for="tagItem in workItem.workEmphasisList"
              :key="tagItem"
              type="info">
              {{ tagItem }}
            </ElTag>
          </ElSpace>
        </ElSpace>
      </ElSpace>
    </ElSpace>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const spacer = h('span', '·')
  const spacerDivider = h(ElDivider, {
    direction: 'horizontal',
    style: {
      margin: 0,
    },
  })

  const data = computed(() => {
    return info?.geekWorkExpList || []
  })
</script>

<style lang="scss" scoped>
  .info_item {
    .item_title {
      position: relative;
      margin-bottom: 16px;
      color: #606266;
      font-size: 18px;
      line-height: 26px;
      &::before {
        position: absolute;
        top: 50%;
        left: -12px;
        width: 6px;
        height: 6px;
        transform: translateY(-50%);
        border-radius: 50%;
        background: #0055ff;
        content: '';
      }
    }
  }
</style>
