const errorRoutes = [
  // 无权限
  {
    path: '/403',
    component: () => import('./PageForbidden.vue'),
    meta: {
      title: '无权限访问',
    },
  },
  // 未找到
  {
    path: '/404',
    component: () => import('./PageNotFond.vue'),
    meta: {
      title: '页面不存在',
    },
  },
  // 获取信息失败
  {
    path: '/info-error',
    component: () => import('./PageInfoError.vue'),
    meta: {
      title: '获取信息失败',
    },
  },
]

export default errorRoutes
