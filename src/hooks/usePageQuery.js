function initWithRoute(object) {
  const route = useRoute()

  return Object.keys(object).reduce((acc, key) => {
    // 根据初始值判断类型来决定是否需要转换
    if (Array.isArray(object[key])) {
      if (route.query[key] !== undefined) {
        // 如果是字符串，尝试解析为数组
        if (typeof route.query[key] === 'string') {
          try {
            acc[key] = JSON.parse(route.query[key])
          } catch {
            // 解析失败时，用初始值
            acc[key] = object[key]
          }
        } else {
          acc[key] = route.query[key]
        }
      } else {
        acc[key] = object[key]
      }
    } else if (typeof object[key] === 'number') {
      if (route.query[key] !== undefined) {
        acc[key] = Number(route.query[key])
      } else {
        acc[key] = object[key]
      }
    } else if (typeof object[key] === 'boolean') {
      if (route.query[key] !== undefined) {
        acc[key] = route.query[key] === 'true'
      } else {
        acc[key] = object[key]
      }
    } else {
      acc[key] = route.query[key] || object[key]
    }

    return acc
  }, {})
}

/**
 * @template {Record<string, any>} T
 * @template {Record<string, any>} U
 * @param {Object} options
 * @param {T} options.search 搜索参数
 * @param {U} options.pagination 分页参数
 * @param {Array<string>} options.excludeKeys 排除的参数
 * @returns {{ searchData: import("vue").Reactive<T>, paginationData: import("vue").Reactive<U>, updateRouteQuery: () => void }}
 */
export function useRouteQuery({ search = {}, pagination = {}, excludeKeys = [] } = {}) {
  const route = useRoute()
  const router = useRouter()

  const searchData = reactive(initWithRoute(search))

  const paginationData = reactive(initWithRoute(pagination))

  const updateRouteQuery = () => {
    const query = {
      ...route.query,
      ...searchData,
      ...paginationData,
    }

    // 处理数组类型参数的序列化
    Object.keys(query).forEach((key) => {
      if (Array.isArray(query[key])) {
        if (query[key].length === 0) {
          delete query[key]
        } else {
          query[key] = JSON.stringify(query[key])
        }
      }
    })

    // 排除不需要的参数
    excludeKeys.forEach((key) => {
      delete query[key]
    })
    // 去掉空值
    Object.keys(query).forEach((key) => {
      if (query[key] === '' || query[key] === null || query[key] === undefined) {
        delete query[key]
      }
    })
    router.replace({ query })
  }

  return {
    searchData,
    paginationData,
    updateRouteQuery,
  }
}
