export function addCommasToNumber(num) {
  // 将输入转换为字符串，确保可以处理字符串和数字输入
  let numStr = String(num)

  // 检查是否是有效的数字
  if (
    !numStr
      .replace('.', '', 1)
      .replace('-', '', 1)
      .match(/^[0-9]+(\.[0-9]+)?$/)
  ) {
    throw new Error('输入必须是一个数字或数字字符串')
  }

  // 分离整数部分和小数部分（如果有的话）
  let parts = numStr.split('.')
  let integerPart = parts[0]
  let decimalPart = parts[1] || ''

  // 从右到左每隔三位加一个逗号
  let integerPartWithCommas = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 如果有小数部分，重新组合
  if (decimalPart) {
    return `${integerPartWithCommas}.${decimalPart}`
  } else {
    return integerPartWithCommas
  }
}
