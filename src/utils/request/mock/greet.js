import { defineMock } from '@alova/mock'

// 模拟数据 - 假设有100条打招呼记录
const mockData = Array.from({ length: 100 }, (_, index) => ({
  bid: `180878981909219${3280 + index}`,
  positionName: [
    'Java开发工程师',
    'Python开发工程师',
    '前端开发工程师',
    '产品经理',
    'UI设计师',
    '测试工程师',
    '运营专员',
    '数据分析师',
  ][index % 8],
  positionType: ['社招全职', '应届校园招聘', '兼职招聘', '实习生招聘'][index % 4],
  status: ['已发布', '已关闭', '发布中', '发布失败', '草稿'][index % 5],
  releaseChannel: 'BOSS直聘',
  greetStatus: [1, 2, 3, 4][index % 3],
  isGreeted: [0, 1][index % 3],
  greetCount: Math.floor(Math.random() * 50) + 1,
  matchTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0],
}))
// 候选人模拟数据
const candidateMockData = Array.from({ length: 80 }, (_, index) => ({
  bid: `candidate_${180878981909219 + index}`,
  name: [
    '张三',
    '李四',
    '王五',
    '赵六',
    '陈七',
    '刘八',
    '杨九',
    '黄十',
    '周一',
    '吴二',
    '郑三',
    '王四',
    '冯五',
    '陈六',
    '褚七',
    '卫八',
  ][index % 16],
  age: 22 + (index % 15),
  p_经验: ['1-3年', '3-5年', '5-10年', '应届生', '10年以上'][index % 5],
  p_最高学历: ['本科', '硕士', '博士', '大专'][index % 4],
  p_最近工作经历: [
    '腾讯科技 - 高级前端工程师',
    '阿里巴巴 - Java开发工程师',
    '字节跳动 - 产品经理',
    '美团 - 数据分析师',
    '百度 - Python工程师',
    '京东 - UI设计师',
    '滴滴出行 - 测试工程师',
    '小米科技 - 运营专员',
  ][index % 8],
  p_匹配度: `${60 + (index % 40)}%`,
  greetStatus: [1, 2, 3, 4][index % 3],
  isGreeted: [0, 1][index % 3],
  details: JSON.stringify(
    JSON.stringify({
      geekDetail: {
        geekBaseInfo: {
          name: '郑汪洋',
          large: 'https://img.bosszhipin.com/boss/avatar/avatar_15.png',
          tiny: 'https://img.bosszhipin.com/boss/avatar/avatar_15.png',
          gender: 1,
          userDescription:
            '1. 专业成绩较好，逻辑思维能力强，思路清楚，学习能力强，对计算机技术有着强烈的好奇心。\n2. 对工作尽职尽责，能够全身心的为工作奉献。\n3. 能够尽快熟悉业务，能快速融入团队。\n4. 乐于与用户以及同事和领导沟通，以便快速解决项目遇到的问题。\n5. 做事勤勉，服从领导命令。',
          activeTimeDesc: '刚刚活跃',
          ageDesc: '22岁',
          workYearDesc: '25年应届生',
          degreeCategory: '本科',
          userDesc:
            '1. 专业成绩较好，逻辑思维能力强，思路清楚，学习能力强，对计算机技术有着强烈的好奇心。\n2. 对工作尽职尽责，能够全身心的为工作奉献。\n3. 能够尽快熟悉业务，能快速融入团队。\n4. 乐于与用户以及同事和领导沟通，以便快速解决项目遇到的问题。\n5. 做事勤勉，服从领导命令。',
          applyStatusContent: '在校-月内到岗',
        },
        geekExpectList: [
          {
            locationName: '武汉',
            positionName: '测试',
            industryDesc: '行业不限',
            salaryDesc: '3-5K',
            industryExpect: false,
            positionTagName: null,
          },
          {
            locationName: '武汉',
            positionName: '运维/技术支持',
            industryDesc: '行业不限',
            salaryDesc: '4-5K',
            industryExpect: false,
            positionTagName: null,
          },
          {
            locationName: '武汉',
            positionName: 'C++',
            industryDesc: '行业不限',
            salaryDesc: '3-4K',
            industryExpect: false,
            positionTagName: null,
          },
          {
            locationName: '武汉',
            positionName: 'Java',
            industryDesc: '行业不限',
            salaryDesc: '3-4K',
            industryExpect: false,
            positionTagName: null,
          },
          {
            locationName: '武汉',
            positionName: '前端/移动开发',
            industryDesc: '行业不限',
            salaryDesc: '4-5K',
            industryExpect: false,
            positionTagName: null,
          },
          {
            locationName: '武汉',
            positionName: '电子/硬件开发',
            industryDesc: '行业不限',
            salaryDesc: '6-7K',
            industryExpect: false,
            positionTagName: null,
          },
        ],
        geekWorkExpList: [
          {
            workId: 228827511837190,
            startYearMonStr: '2025.03',
            endYearMonStr: '至今',
            company: '群思科技（武汉）有限公司',
            positionName: '测试运维工程师',
            positionTitle: '',
            department: '',
            responsibility:
              '1、系统功能测试：负责ITSM、OA、ERP系统的功能测试，主要参与新模块测试，执行测试用例、提交问题单、跟踪修复及回归验证。\n2、接口测试：使用JMeter编写测试脚本，验证基础接口功能和基础性能。\n3、系统版本升级与部署验证：独立负责ITSM+OA系统在测试环境从基准版本逐步升级至最新版本的部署包更新工作，执行升级后的基础功能冒烟测试和日志分析，记录部署和测试过程中遇到的问题，协助开发团队定位问题，最终输出部署测试报告。',
            workPerformance: '',
            workEmphasisList: ['Linux', 'Java', 'Jmeter', 'Selenium', 'Postman'],
          },
        ],
        geekProjExpList: [
          {
            projectId: 228769174011905,
            descriptionHighlightList: null,
            performanceHighlightList: null,
            name: '个人博客管理后端',
            url: '',
            roleName: '项目组长',
            description:
              '开发工具及环境:IntelliJ IDEA、JDK1.8、MySQL、Navicat\n应用技术:SpringCloud、SpringBoot、Gateway、Nacos、Maven、MybatisPlus.\n Knife4j、Redis、Sentinel、Feign、Druid、Lombok\n项目描述:后端使用SpringCloud来实现微服务;通过Gateway来进行访问过滤和路由通过Nacos来进行配置的动态管理,以便快速配置;使用MybatisPlus来处理数据库相关的操作;使用Knife4j来快捷的进行接口测试;使用Redis缓存提高数据访问速度;使用\nSentinel的熔断降级来保证服务的可用性;使用Feign来使服务间能够相互调用。\n负责模块:后端所有接口和服务。\n1.先将项目分为四个模块:core模块、gateway模块、user模块、posts模块\n2.core模块中:包含ResultVo对象和相关工具类,负责给前端发送统一的数据集。\n3.gateway模块来进行访问过滤和路由,配置了负载均衡,通过Nacos的服务与发现来寻找对应的服务。\n4.posts模块中:包含posts相关实体类和对应的方法,查询接口使用了Redis缓存。\n5.user模块中:包含user相关实体类和对应的方法,通过Feign来访问posts模块的方法;用户登录接口实现了熔断降级,保证服务可用',
            performance: '',
            orderNum: 0,
            startDate: '20241101',
            endDate: '20241201',
            startYearMonStr: '2024.11',
            endYearMonStr: '2024.12',
          },
          {
            projectId: 228768903241774,
            descriptionHighlightList: null,
            performanceHighlightList: null,
            name: '音乐管理系统',
            url: '',
            roleName: '项目组长',
            description:
              '开发工具及环境:IntelliJ IDEA、JDK1.8、MySQL、Navicat\n应用技术:SpringBoot、Maven、Mybatis、Swagger、Druid、Lombok\n1.使用Vue进行前端的分模块编写,使用Vue-Router进行路由跳转页面;\n2.使用ElementUI来设计前端界面,使用Axios来进行后端请求\n3.使用关联表来将歌曲和用户关联起来,实现用户独立歌单的功能;\n4.使用经典的三层架构,通过Controller调用Servicelmpl,Servicelmpl调用DAO层实现歌曲的增删改查;\n5.编辑Mapper文件使用Mybatis动态SQL技术对歌曲的批量增删操作具体实现',
            performance: '',
            orderNum: 0,
            startDate: '20241001',
            endDate: '20241101',
            startYearMonStr: '2024.10',
            endYearMonStr: '2024.11',
          },
          {
            projectId: 59234155,
            descriptionHighlightList: null,
            performanceHighlightList: null,
            name: '小星智能家居系统的设计与实现',
            url: '',
            roleName: '项目组长',
            description:
              '基于Cortex-A9的家居控制系统，可以通过上位机软件来控制不同设备的状态也可以用语音控制。\n开发工具及环境：Ubuntu22.04、VSCode、QT6。\n负责模块：服务器转发模块、开发板系统模块、上位机部分模块。\n运用技术：\n1. 开发板使用状态机编程思想来实现功能的选择。\n2. 使用百度语音接口来识别语音。\n3. 上位机软件与服务器的数据交互使用Socket 完成。\n4. 服务器与开发板的数据交互通过UART串口实现。\n5. 服务器应答部分使用多线程完成。\n6.使用硬件中断来更改执行状态。\n7. QT使用信号与槽来调用不同的槽函数。\n8.使用I2C读取MPU6050的温度数据。',
            performance: '',
            orderNum: 0,
            startDate: '20240601',
            endDate: '20240701',
            startYearMonStr: '2024.06',
            endYearMonStr: '2024.07',
          },
          {
            projectId: 59234154,
            descriptionHighlightList: null,
            performanceHighlightList: null,
            name: '智能多媒体播放器',
            url: '',
            roleName: '项目组长',
            description:
              '基于Cortex-A53的智能多媒体播放器，可以实现浏览相册、影音播放、语音操作的功能。\n开发工具及环境：Ubuntu22.04、VSCode。\n负责模块：浏览相册、语音操作模块。\n运用技术：\n1.使用状态机编程思想来实现功能的选择。\n2.使用内存映射来快速显示图片到LCD屏幕上。\n3.使用多线程技术来控制图片播放。\n4.使用mplayer 播放音乐和视频。\n5.使用多进程技术控制音乐视频的播放/暂停和切换。\n6.开发板通过Socket 协议来实现语音识别功能的数据交互。',
            performance: '',
            orderNum: 0,
            startDate: '20240101',
            endDate: '20240201',
            startYearMonStr: '2024.01',
            endYearMonStr: '2024.02',
          },
          {
            projectId: 59234153,
            descriptionHighlightList: null,
            performanceHighlightList: null,
            name: 'VFD电子时钟',
            url: '',
            roleName: '项目组长',
            description:
              '基于CH552芯片和VFD屏幕开发的一个小时钟。使用DS3231搭配纽扣电池计时，实现断电后继续走时的功能。\n开发环境：Keil 5，烧录工具WchIspStudio。\n运用技术：\n1. 使用状态机编程思想来切换时间设置，日期显示等不同操作。\n2. 使用DS3231时钟芯片来完成计时和温度测量的功能。\n3.使用VFD屏幕进行时间和动画的显示。\n4.使用I2C总线来完成CH552和DS3231之间的相互通信。',
            performance: '',
            orderNum: 0,
            startDate: '20230201',
            endDate: '20230301',
            startYearMonStr: '2023.02',
            endYearMonStr: '2023.03',
          },
        ],
        geekEduExpList: [
          {
            courseDesc: '数据结构与算法、计算机操作系统、Linux、C/C++、数字逻辑',
            startYearStr: '2021',
            endYearStr: '2025',
            badge:
              'https://img.bosszhipin.com/beijin/icon/895585b6ae640d96a8af8dfe440b4c84b1792042df7d374e83c5b195df57be51.png',
            school: '武汉华夏理工学院',
            major: '计算机科学与技术',
            degreeName: '本科',
            eduType: 1,
            tags: [],
            eduDescription:
              '1、在班级中担任心理委员，举办过心理晚会活动。\n2、在校期间获得过校级三等奖学金',
            majorRankingDesc: '专业前5%',
            thesisTitle: '基于Arduino智能门锁的设计与实现',
            thesisDesc:
              '本项目采用乐鑫科技出品的 ESP32S3 主控芯片，该芯片内部集成了 Wi-Fi 通信模块和蓝牙通信模块。项目主要选用 HLK-FPM383C 半导体指纹识别模块和 3 * 4 矩阵键盘模块作为输入设备，远程通信近距离使用 Wi-Fi 通信传输指令，中远距离则通过 MQTT 协议向点灯科技提供的服务器进行交互，最后转发到开发板执行相关指令。用户端基于点灯科技提供的 App 进行二次开发，开发后的 App 在开锁方面支持远程开锁功能；指纹模块方面支持添加指纹、删除指纹和清除指纹功能；陌生人来访场景支持生成临时密码，临时录入指纹功能；异常检测模块支持密码错误次数报警和电池低电压报警功能。',
          },
        ],
        highestEduExp: {
          courseDesc: '数据结构与算法、计算机操作系统、Linux、C/C++、数字逻辑',
          startYearStr: '2021',
          endYearStr: '2025',
          badge:
            'https://img.bosszhipin.com/beijin/icon/895585b6ae640d96a8af8dfe440b4c84b1792042df7d374e83c5b195df57be51.png',
          school: '武汉华夏理工学院',
          major: '计算机科学与技术',
          degreeName: '本科',
          eduType: 1,
          tags: [],
          eduDescription:
            '1、在班级中担任心理委员，举办过心理晚会活动。\n2、在校期间获得过校级三等奖学金',
          majorRankingDesc: '专业前5%',
          thesisTitle: '基于Arduino智能门锁的设计与实现',
          thesisDesc:
            '本项目采用乐鑫科技出品的 ESP32S3 主控芯片，该芯片内部集成了 Wi-Fi 通信模块和蓝牙通信模块。项目主要选用 HLK-FPM383C 半导体指纹识别模块和 3 * 4 矩阵键盘模块作为输入设备，远程通信近距离使用 Wi-Fi 通信传输指令，中远距离则通过 MQTT 协议向点灯科技提供的服务器进行交互，最后转发到开发板执行相关指令。用户端基于点灯科技提供的 App 进行二次开发，开发后的 App 在开锁方面支持远程开锁功能；指纹模块方面支持添加指纹、删除指纹和清除指纹功能；陌生人来访场景支持生成临时密码，临时录入指纹功能；异常检测模块支持密码错误次数报警和电池低电压报警功能。',
        },
        geekSocialList: null,
        geekVolunteerExpList: null,
        geekDzDoneWorkList: null,
        geekCertificationList: null,
        geekDoneWorkList: [],
        highlightWords: null,
        certList: null,
        geekDesignWorksList: null,
        geekDesignWorksGather: null,
        geekPersonalImageList: null,
        geekDeliciousFoodImageList: null,
        geekPersonalLabelList: null,
        geekPostExpList: null,
        geekHandicappedInfo: null,
        geekTrainingExpList: [],
        geekHonorList: [],
        geekCustomInterestConfig: null,
        geekJobHuntGroupMemberList: null,
        geekClubExpList: [],
        professionalSkill:
          '熟悉C语言，有良好的编程习惯。\n熟悉C++编程，熟悉C++面向对象编程思想。\n熟悉Keil 5的使用，独立完成过项目。\n熟悉多线程开发、熟练掌握网络编程。\n熟悉使用Git 工具PULL和PUSH代码。\n掌握Linux下的Shell 脚本编程以及Makefile的编写使用。\n了解嵌入式Linux驱动开发。\n了解UART、I2C、SPI 和CAN通信协议。\n了解Cortex-A53、Cortex-A9开发。\n了解模电、数电等电路知识，有一定的画PCB板和焊接贴片芯片的相关经验。',
        overseasTraitOptions: null,
      },
      securityId:
        'p8GIIHhc0GZtg-L1UoE6PFPIwnunpm2JXJ9T0X2abReocfor_edwvPk0Cygn9hTmdougbX4LQ_Lof-S1EFftKpQQCRlbqzNijoeTKHL-s-tv60NIIT_H1VIFS8JImSNQh2Pnf4tIN_E1hTcnzQaTY53_jMnth8oetdJSESDPX_H4sPh9BA93RohpOVxBrWvHYf2q1mz9IvPAWQt-TIdoW9GcH-23f4a_dSUX8Y9aiVSQmzNb2tiIJsBmrujwiSDk_ENjD9KPGQMS4dtQDSQP27u1bdEEXbIgCGwXez9thSjOrTeZQsTGeNqeKKsIJ34Cubc8Repy5QcdOJ20VBUNynRDqOSHSWZP9hHwX-d8kZ4gGAqVAZiev8yOV5O3wl_OFOUo37Gk6u1JdjR6T8DlY6FUWQYkCNvRu0-PQ1fUvx4gIUKxuL8ut1y4Uq_q5yaKesXDa3KQ57fOjHHiwJAolh01mN3tjo4AWcvbDpXU2mOZC_lxWFd_bE9dEbqZObLwX1iCHmmktoz3dBDdTzUpg0owLJ-sJJ6VdGK36oftWIIVdaVcm9R8XxHzlK4w7yQNaMmblewuvb2MYFXoLRP56Rtz6DRRTW_kpf92NgMHr-gswlJZ9lxkn2OCEFY4adhcSiM8iDCaC355vn1meLrfdSEWr0zhg48CCYY_PineveGDuLUJBDklTKMFZIU~',
      geekQuestInfo: null,
      segs: null,
      eliteGeek: 1,
      searchChatCardCostCount: 2,
      vipCostChatCount: 0,
      jobCompetitiveTags: [
        '薪资竞争力',
        '沟通竞争力',
        '牛人受欢迎程度',
        '牛人公司规模偏好',
        '牛人沟通时间偏好',
      ],
      encryptBossId: '8f699ea42121f3150nF_0tq-F1tQ',
      expectId: 1186625702,
      encryptExpectId: 'ed883544de78c7c81nVy3du_FVVQwA~~',
      encryptGeekId: '426404f032acfa260nN92969E1pT',
      encryptJobId: '689c1fe44ee3b1f603F82tW8ElJR',
      alreadyInterested: 1,
      supportInterested: 1,
      eduCheckRes: null,
      workCheckRes: null,
      showExpectPosition: {
        locationName: '武汉',
        positionName: '测试',
        industryDesc: '行业不限',
        salaryDesc: '3-5K',
        industryExpect: false,
        positionTagName: null,
      },
      jobCompetitive: null,
      bossViewGeekWorkExp: {
        geekWorkPositionExpDescList: ['测试工程师 5个月'],
        showJobExperienceTip: '',
      },
      anonymousChatItemOptions: {
        available: 1,
        availableSelect: 0,
        options: [],
        selectedItem: 19,
      },
      itemCallShowInfo: null,
      sendFreeGeek: 0,
      bottomTip: null,
      hunterIntentionEntrance: {
        showEntrance: false,
        text: null,
        cardType: 0,
        hasIntention: 0,
        popUpWindow: 0,
        popUpText: null,
      },
      rcdReason: null,
      bottomBubbleTip: null,
      resumeDeliverInfo: null,
      hasUseChatHelper: 0,
      moduleDataList: [],
    }),
  ),
  // 打招呼时间
  greetTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0],
  // 匹配度
  matchPercentage: Math.floor(Math.random() * 40) + 60 + '%',
}))

const greetMock = defineMock(
  {
    '[post]/api/v1/greet/list': ({ data }) => {
      const page = parseInt(data.page.number) || 1
      const pageSize = parseInt(data.page.pageSize) || 10

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const resData = mockData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: mockData.length,
          list: resData,
          hasNext: end < mockData.length,
        },
        msg: '成功',
      }
    },
    '[POST]/api/v1/greet/match/candidate/pagelist': ({ data }) => {
      const page = parseInt(data.pageNum) || 1
      const pageSize = parseInt(data.pageSize) || 10

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const resData = candidateMockData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: candidateMockData.length,
          list: resData,
          hasNext: end < candidateMockData.length,
        },
        msg: '成功',
      }
    },
  },
  false,
)

export default greetMock
