import errorRoutes from '@/router/error-routes'

/**
 * api接口地址
 */
export const apiBaseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 应用appId
 */
export const clientId = import.meta.env.VITE_PROJECT_PREFIX

/**
 * 权限空间Id
 */
export const spaceId = import.meta.env.VITE_SPACE_ID

/**
 * 统一认证平台登录地址
 */
export function getSsoLoginUrl(redirect = '') {
  // 当前路径
  const currentPath = window.location.pathname
  // 错误页面
  const errorPaths = errorRoutes.map((item) => item.path)
  // 如果是错误页面则跳转到首页，否则跳转到指定页面或当前页面
  const defaultRedirect = errorPaths.includes(currentPath)
    ? window.location.origin
    : redirect || window.location.href
  const redirect_uri = encodeURIComponent(defaultRedirect)

  return (
    apiBaseUrl +
    `/openapi/sso/login?clientId=${getClientId() || clientId}&redirect_uri=${redirect_uri}`
  )
}

/**
 * kkFileView预览地址
 */
export const kkFileViewUrl = import.meta.env.VITE_KK_FILE_VIEW_URL

/**
 * 本地存储的 key
 */
export const localStorageKey = {
  token: import.meta.env.VITE_PROJECT_PREFIX + '_token',
  userInfo: import.meta.env.VITE_PROJECT_PREFIX + '_userInfo',
  roleInfo: import.meta.env.VITE_PROJECT_PREFIX + '_roleInfo',
  clientId: import.meta.env.VITE_PROJECT_PREFIX + '_clientId',
}

/**
 * geojson地址
 */
export const geoApiUrl = import.meta.env.VITE_APP_GEO_JSON_BASE

/**
 * 获取 token
 * @returns
 */
export const getToken = () => {
  return localStorage.getItem(localStorageKey.token)
}

/**
 * 设置 token
 * @param {*} value
 */
export const setToken = (value) => {
  localStorage.setItem(localStorageKey.token, value)
}

/**
 * 清除 token
 */
export const removeToken = () => {
  localStorage.removeItem(localStorageKey.token)
}

/**
 * 获取 userInfo
 * @returns
 */
export const getUserInfo = () => {
  return localStorage.getItem(localStorageKey.userInfo)
}

/**
 * 设置 userInfo
 * @param {*} value
 */
export const setUserInfo = (value) => {
  localStorage.setItem(localStorageKey.userInfo, value)
}

/**
 * 清除 userInfo
 */
export const removeUserInfo = () => {
  localStorage.removeItem(localStorageKey.userInfo)
}

/**
 * 获取 roleInfo
 * @returns
 */
export const getRoleInfo = () => {
  return localStorage.getItem(localStorageKey.roleInfo)
}

/**
 * 设置 roleInfo
 * @param {*} value
 */
export const setRoleInfo = (value) => {
  localStorage.setItem(localStorageKey.roleInfo, value)
}

/**
 * 清除 roleInfo
 */
export const removeRoleInfo = () => {
  localStorage.removeItem(localStorageKey.roleInfo)
}

/**
 * 获取  client_id
 */
export const getClientId = () => {
  return localStorage.getItem(localStorageKey.clientId)
}

/**
 * 设置 client_id
 */
export const setClientId = (value) => {
  localStorage.setItem(localStorageKey.clientId, value)
}

/**
 * 清除 client_id
 */
export const removeClientId = () => {
  localStorage.removeItem(localStorageKey.clientId)
}
