<template>
  <TablePageLayout title="职位管理">
    <template #header-extra>
      <ElButton
        :text="true"
        type="primary"
        @click="handleCreate">
        <template #icon>
          <i class="iconfont icon-circle-plus text-[12px]!" />
        </template>
        <span class="ml-[4px]">发布职位</span>
      </ElButton>
    </template>
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="职位名称"
          prop="positionName">
          <ElInput
            v-model="searchData.positionName"
            style="width: 260px"
            placeholder="请输入职位名称" />
        </ElFormItem>
        <ElFormItem
          label="职位类型"
          prop="positionType">
          <ElSelect
            v-model="searchData.positionType"
            style="width: 260px"
            placeholder="请选择职位类型"
            :empty-values="[null, undefined]"
            :clearable="true">
            <ElOption
              value=""
              label="不限"></ElOption>
            <ElOption
              v-for="item in JOB_TYPE"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="职位状态"
          prop="status">
          <ElSelect
            v-model="searchData.status"
            style="width: 260px"
            placeholder="请选择职位状态"
            :empty-values="[null, undefined]"
            :clearable="true">
            <ElOption
              value=""
              label="不限"></ElOption>
            <ElOption
              v-for="item in JOB_STATUS"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="所属账号"
          prop="createdByName">
          <UserNameInput
            v-model="searchData.createdByName"
            style="width: 260px" />
        </ElFormItem>
        <ElFormItem
          label="发布渠道"
          prop="releaseChannel">
          <ElSelect
            v-model="searchData.releaseChannel"
            style="width: 260px"
            placeholder="请选择发布渠道"
            :empty-values="[null, undefined]"
            :clearable="true">
            <ElOption
              value=""
              label="不限"></ElOption>
            <ElOption
              v-for="item in JOB_FBQD"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            :loading="tableLoading"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="tableLoading"
        :max-height="maxHeight"
        :border="true"
        :data="tableData">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无职位信息" />
        </template>
        <ElTableColumn
          label="职位ID"
          prop="bid"
          :show-overflow-tooltip="true" />
        <ElTableColumn
          label="职位名称"
          prop="extraData"
          :show-overflow-tooltip="true" />
        <!-- <ElTableColumn
          label="职位分类"
          prop="positionName"
          :show-overflow-tooltip="true" /> -->
        <ElTableColumn
          label="职位类型"
          prop="positionType">
          <template #default="{ row }">
            {{ getDictLabel('JOB_TYPE', row.positionType) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="职位状态"
          prop="status">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  已发布: 'success',
                  已关闭: 'info',
                  发布中: 'warning',
                  发布失败: 'danger',
                  草稿: 'primary',
                }[row.status]
              ">
              {{ getDictLabel('JOB_STATUS', row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="发布渠道"
          prop="releaseChannel">
          <template #default="{ row }">
            <!--
              若职位处于发布失败、草稿，则发布渠道显示“-”
            -->
            {{
              row.status === '发布失败' || row.status === '草稿'
                ? '-'
                : row.releaseChannel || '暂无数据'
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="关联简历数"
          prop="resumeCount">
          <template #default="{ row }">
            <ElButton
              v-if="row.resumeCount > 0"
              :link="true"
              type="primary"
              @click="handleViewResume(row)">
              {{ row.resumeCount }}
            </ElButton>
            <ElButton
              v-else
              :link="true"
              type="info"
              @click="handleViewResume(row)">
              {{ row.resumeCount }}
            </ElButton>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="所属部门"
          prop="deptName" />
        <ElTableColumn
          label="所属账号"
          prop="createdByName" />
        <ElTableColumn
          label="最新发布时间"
          prop="releaseTime"
          width="180" />
        <ElTableColumn
          label="操作"
          width="220">
          <template #default="{ row }">
            <!-- 发布中时禁用查看 -->
            <ElButton
              :link="true"
              type="primary"
              :disabled="row.status === '发布中'"
              @click="handleView(row)">
              查看
            </ElButton>
            <!-- 已发布、已关闭且有简历关联、发布中时禁用编辑 -->
            <ElButton
              :link="true"
              type="primary"
              :disabled="
                row.status === '已发布' ||
                (row.status === '已关闭' && row.resumeCount > 0) ||
                row.status === '发布中'
              "
              @click="handleEdit(row)">
              编辑
            </ElButton>
            <!-- 已关闭、发布失败、草稿时显示发布按钮 -->
            <!-- <ElPopconfirm
              v-if="row.status === '已关闭' || row.status === '发布失败' || row.status === '草稿'"
              :visible="publishPopConfirmVisible === row.bid"
              title="是否确认发布该职位？发布后会同步到 BOSS 直聘上。"
              width="235px"
              @confirm="handlePublishConfirm(row)"
              @cancel="handlePublishCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="primary"
                  :disabled="row.status === '草稿'"
                  :loading="publishLoading === row.bid"
                  @click="handlePublish(row)">
                  发布
                </ElButton>
              </template>
            </ElPopconfirm> -->
            <ElButton
              v-if="row.status === '已关闭' || row.status === '发布失败' || row.status === '草稿'"
              :link="true"
              type="primary"
              :disabled="row.status === '草稿'"
              @click="handlePublish(row)">
              发布
            </ElButton>
            <!-- 已发布、发布中时显示关闭按钮 -->
            <ElPopconfirm
              v-if="row.status === '已发布' || row.status === '发布中'"
              :visible="closePopConfirmVisible === row.bid"
              title="是否确认关闭该职位？关闭后该职位将不再接受简历投递。"
              width="235px"
              @confirm="handleCloseConfirm(row)"
              @cancel="handleCloseCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="primary"
                  :disabled="row.status === '发布中'"
                  @click="handleClose(row)">
                  关闭
                </ElButton>
              </template>
            </ElPopconfirm>
            <!-- 已发布、已关闭且有简历、发布中时关联时禁用删除 -->
            <ElPopconfirm
              :visible="deletePopConfirmVisible === row.bid"
              title="是否确认删除该职位？删除后该职位将不再对外展示。"
              width="235px"
              @confirm="handleDeleteConfirm(row)"
              @cancel="handleDeleteCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="danger"
                  :disabled="
                    row.status === '已发布' ||
                    (row.status === '已关闭' && row.resumeCount > 0) ||
                    row.status === '发布中'
                  "
                  @click="handleDelete(row)">
                  删除
                </ElButton>
              </template>
            </ElPopconfirm>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange" />
    </template>
  </TablePageLayout>
  <FbqdSelectDialog
    v-model="fbqdSelectDialogOptions.visible"
    :loading="fbqdSelectDialogOptions.loading"
    @confirm="onFbqdSelectDialogConfirm"></FbqdSelectDialog>
</template>

<script setup>
  import { closeJob, deleteJob, getJobList, openJob } from '@/apis/job'
  import { TablePageLayout, UserNameInput } from '@/components'
  import { useRouteQuery } from '@/hooks/usePageQuery'
  import { getDictLabel, useDict } from '@/stores/dict'
  import { filterEmptyValues } from '@/utils/obj'
  import FbqdSelectDialog from './components/fbqd-select-dialog.vue'
  import { checkLogin } from './utils'
  const router = useRouter()
  // const route = useRoute()
  const { JOB_TYPE, JOB_STATUS, JOB_FBQD } = useDict('JOB_TYPE', 'JOB_STATUS', 'JOB_FBQD')

  const { searchData, paginationData, updateRouteQuery } = useRouteQuery({
    search: {
      positionName: '',
      positionType: '',
      status: '',
      createdByName: '',
      releaseChannel: '',
    },
    pagination: {
      pageSize: 10,
      pageNum: 1,
      total: 0,
    },
    excludeKeys: ['total'],
  })

  // 搜索表单
  const searchFormRef = useTemplateRef('searchFormRef')
  // const searchData = reactive({
  //   positionName: '',
  //   positionType: '',
  //   status: route.query.status || '',
  // })
  // 查询
  function handleSearch() {
    getTableData()
  }
  // 重置
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  // 表格数据
  const tableData = ref([])
  const tableLoading = ref(false)
  // 获取表格数据
  function getTableData() {
    tableLoading.value = true
    getJobList({
      ...filterEmptyValues(searchData),
      pageSize: paginationData.pageSize,
      pageNum: paginationData.pageNum,
    })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page

        updateRouteQuery()
      })
      .finally(() => {
        tableLoading.value = false
      })
  }

  onMounted(() => {
    getTableData()
  })

  // 发布职位
  function handleCreate() {
    router.push({
      path: '/jobs/create',
    })
  }

  function handleViewResume(row) {
    router.push({
      path: '/resume/talents',
      query: {
        position: row.positionName,
        positionBid: row.bid,
        matchResult: '不限',
      },
    })
  }

  // 查看
  function handleView(row) {
    router.push({
      path: '/jobs/detail',
      query: {
        jobId: row.bid,
      },
    })
  }

  // 编辑
  function handleEdit(row) {
    router.push({
      path: '/jobs/edit',
      query: {
        jobId: row.bid,
      },
    })
  }

  // 关闭
  const closePopConfirmVisible = ref(null)
  const closeLoading = ref(null)
  function handleClose(row) {
    closeLoading.value = row.bid
    checkLogin()
      .then((isLogin) => {
        if (isLogin) {
          closePopConfirmVisible.value = row.bid
        } else {
          closePopConfirmVisible.value = null
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        closeLoading.value = null
      })
  }
  // 确认关闭
  function handleCloseConfirm(row) {
    closeLoading.value = row.bid
    closeJob({ bid: row.bid })
      .then(() => {
        ElMessage.success('职位已关闭')
        getTableData()
      })
      .finally(() => {
        closeLoading.value = null
      })
    closePopConfirmVisible.value = null
  }
  // 取消关闭
  function handleCloseCancel() {
    closePopConfirmVisible.value = null
  }

  // 删除
  const deletePopConfirmVisible = ref(null)
  const deleteLoading = ref(null)
  function handleDelete(row) {
    deletePopConfirmVisible.value = row.bid
  }
  // 确认删除
  function handleDeleteConfirm(row) {
    deleteLoading.value = row.bid
    deleteJob({ bid: row.bid })
      .then(() => {
        ElMessage.success('删除成功')
        getTableData()
      })
      .finally(() => {
        deleteLoading.value = null
      })
    deletePopConfirmVisible.value = null
  }
  // 取消删除
  function handleDeleteCancel() {
    deletePopConfirmVisible.value = null
  }

  // 分页
  // const paginationData = reactive({
  //   pageSize: 10,
  //   pageNum: 1,
  //   total: 0,
  // })
  // 改变每页条数
  function handlePageSizeChange() {
    paginationData.pageNum = 1
    getTableData()
  }
  // 改变当前页
  function handleCurrentPageChange() {
    getTableData()
  }

  const fbqdSelectDialogOptions = reactive({
    visible: false,
    loading: false,
    data: {},
  })
  const onFbqdSelectDialogConfirm = (fbqd) => {
    fbqdSelectDialogOptions.loading = true
    checkLogin()
      .then((isLogin) => {
        if (isLogin) {
          handlePublishConfirm({ ...fbqdSelectDialogOptions.data, fbqd })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        fbqdSelectDialogOptions.loading = false
      })
  }
  // 发布
  function handlePublish(row) {
    fbqdSelectDialogOptions.visible = true
    fbqdSelectDialogOptions.data = row
  }
  // 确认发布
  function handlePublishConfirm(row) {
    fbqdSelectDialogOptions.loading = true
    openJob({ bid: row.bid, releaseChannel: row.releaseChannel })
      .then(() => {
        ElMessage.success('职位已发布')
        getTableData()
        fbqdSelectDialogOptions.visible = false
      })
      .finally(() => {
        fbqdSelectDialogOptions.loading = false
      })
  }
</script>

<style lang="scss" scoped></style>
