/**
 * 处理AI生成内容的函数
 * @param {*} res
 * @returns
 */
export function processAiResponse(res) {
  let result = null

  const { choices = [] } = res.data
  if (choices.length > 0) {
    const { message: { content = '' } = {} } = choices[0]
    if (content) {
      const parsedContent = JSON.parse(content)
      console.log(parsedContent)
      const { position_description, required_label, select_label, custom_label } = parsedContent

      result = {
        position_description,
        required_label,
        select_label,
        custom_label,
      }

      return result
    }
  }

  return result
}

const generateRange = (start, end, step = 1) => {
  return Array.from({ length: Math.floor((end - start) / step) + 1 }, (_, i) => start + i * step)
}

const generateSalaryOptions = (values, suffix = 'K', append = false) => {
  return values.map((value) => ({ label: value + suffix, value: append ? value + suffix : value }))
}

/**
 * 不同招聘方式的薪资范围
 * @param { String } type 招聘方式： 社招全职、应届校园招聘、兼职招聘、实习生招聘
 * @param {{
 *  method: String,
 *  standard: String,
 * }} options method 结算方式， standard 发放单位
 * @returns {{
 *  min: Array<{ label: string, value: number }>,
 *  max: Array<{ label: string, value: number }>
 *  standard: Array<{ label: string, value: number }>
 * }} 薪资范围
 */
export function getSalaryRange(type, { method = '', unit = '', standard = '' } = {}) {
  if (!type) {
    return {
      min: [],
      max: [],
      standard: [],
    }
  }

  // 社招 和 校招
  if (type === '社招全职' || type === '应届校园招聘') {
    // 下限为 1k - 250k ，其中1K-30K（数字+1递增）、30K-100K（数字+5递增）、100K-250K（数字+10递增）
    const minArr = generateSalaryOptions(
      [...generateRange(1, 30, 1), ...generateRange(35, 100, 5), ...generateRange(110, 250, 10)],
      unit,
    )

    // 上限为 2k - 260k ，其中2K-30K（数字+1递增）、30K-100K（数字+5递增）、100K-250K（数字+10递增）
    const maxArr = generateSalaryOptions(
      [...generateRange(2, 30), ...generateRange(35, 100, 5), ...generateRange(110, 260, 10)],
      unit,
    )

    // 发放月数 12薪 - 24薪，数字+1递增
    const standardArr = generateSalaryOptions([...generateRange(12, 24, 1)], '薪', true)

    return {
      min: minArr,
      max: maxArr,
      standard: standardArr,
    }
  }

  // 兼职
  if (type === '兼职招聘') {
    const minArr = []
    const maxArr = []
    const standardArr = []

    // 日结："元/时"、"元/天"
    // 周结："元/周"
    // 月结："元/月"
    if (method === '日结') {
      standardArr.push({ label: '元/时', value: '时' }, { label: '元/天', value: '天' })
    } else if (method === '周结') {
      standardArr.push({ label: '元/周', value: '周' })
    } else if (method === '月结') {
      standardArr.push({ label: '元/月', value: '月' })
    }

    if (standard === '时') {
      // 下限 5-4900，5-100 数字+5递增，120，150-1000 数字+50递增，1000-4900 数字+100递增
      minArr.push(
        ...generateSalaryOptions(generateRange(5, 100, 5), ''),
        { label: 120 + '', value: 120 },
        ...generateSalaryOptions(generateRange(150, 1000, 50), ''),
        ...generateSalaryOptions(generateRange(1100, 4900, 100), ''),
      )

      // 上限 10-5000，10-100 数字+5递增，120，150-1000 数字+50递增，1000-5000 数字+100递增
      maxArr.push(
        ...generateSalaryOptions(generateRange(10, 100, 5), ''),
        { label: 120 + '', value: 120 },
        ...generateSalaryOptions(generateRange(150, 1000, 50), ''),
        ...generateSalaryOptions(generateRange(1100, 5000, 100), ''),
      )
    }

    if (standard === '天') {
      // 最低薪资范围为50-4900，最高薪资范围为60-10000，其中50-300（数字+10递增）、320、350-1000（数字+50递增）、1000-10000（数字+100递增）
      minArr.push(
        ...generateSalaryOptions(generateRange(50, 300, 10), ''),
        { label: 320 + '', value: 320 },
        ...generateSalaryOptions(generateRange(350, 1000, 50), ''),
        ...generateSalaryOptions(generateRange(1100, 4900, 100), ''),
      )

      maxArr.push(
        ...generateSalaryOptions(generateRange(60, 300, 10), ''),
        { label: 320 + '', value: 320 },
        ...generateSalaryOptions(generateRange(350, 1000, 50), ''),
        ...generateSalaryOptions(generateRange(1100, 10000, 100), ''),
      )
    }

    if (standard === '周') {
      // 最低薪资范围为100-49000，最高薪资范围为200-50000，其中100-1000（数字+100递增）、1200-2100（数字+300递增）、2500-10000（数字+500递增）、10000-50000（数字+1000递增）
      minArr.push(
        ...generateSalaryOptions(generateRange(100, 1000, 100), ''),
        ...generateSalaryOptions(generateRange(1200, 2100, 300), ''),
        ...generateSalaryOptions(generateRange(2500, 10000, 500), ''),
        ...generateSalaryOptions(generateRange(11000, 49000, 1000), ''),
      )

      maxArr.push(
        ...generateSalaryOptions(generateRange(200, 1000, 100), ''),
        ...generateSalaryOptions(generateRange(1200, 2100, 300), ''),
        ...generateSalaryOptions(generateRange(2500, 10000, 500), ''),
        ...generateSalaryOptions(generateRange(11000, 50000, 1000), ''),
      )
    }

    if (standard === '月') {
      // 最低薪资范围为500-250000，最高薪资范围为1000-260000，其中500-10000（数字+500递增）、10000-30000（数字+1000递增）、30000-100000（数字+1000递增）、100000-260000（数字+10000递增）
      minArr.push(
        ...generateSalaryOptions(generateRange(500, 10000, 500), ''),
        ...generateSalaryOptions(generateRange(11000, 30000, 1000), ''),
        ...generateSalaryOptions(generateRange(31000, 100000, 1000), ''),
        ...generateSalaryOptions(generateRange(110000, 250000, 10000), ''),
      )

      maxArr.push(
        ...generateSalaryOptions(generateRange(1000, 10000, 500), ''),
        ...generateSalaryOptions(generateRange(11000, 30000, 1000), ''),
        ...generateSalaryOptions(generateRange(31000, 100000, 1000), ''),
        ...generateSalaryOptions(generateRange(110000, 260000, 10000), ''),
      )
    }

    return {
      min: minArr,
      max: maxArr,
      standard: standardArr,
    }
  }

  // 实习
  if (type === '实习生招聘') {
    // 最低薪资，选项为：10-1000，其中10-1000（数字+10递增）
    const minArr = generateSalaryOptions(generateRange(10, 1000, 10), '')
    // 最高薪资，选项为：20-2000，其中20-2000（数字+10递增）
    const maxArr = generateSalaryOptions(generateRange(20, 2000, 10), '')

    return {
      min: minArr,
      max: maxArr,
    }
  }
}

/**
 * 生成底薪选项
 * @returns {Array<{ label: string, value: number }>} 底薪选项
 */
export function generateBaseSalaryOptions() {
  // 1000元-260000元，选项数字+100递增
  return generateSalaryOptions(generateRange(1000, 260000, 100), '')
}

/**
 * 生成毕业时间选项
 * @returns {Array<{ label: string, value: number }>} 毕业时间选项
 */
export function generateGraduationTimeOptions() {
  const year = new Date().getFullYear()
  // 选项为去年到今年+2
  return generateSalaryOptions(generateRange(year - 1, year + 2, 1), '年')
}

/**
 * 生成最少实习月数选项
 * @returns {Array<{ label: string, value: number }>} 最少实习月数选项
 */
export function generateMinInternshipOptions() {
  // 选项为1-12月，数字+1递增
  return generateSalaryOptions(generateRange(1, 12, 1), '个月')
}

/**
 * 生成最少周到岗天数选项
 * @returns {Array<{ label: string, value: number }>} 最少周到岗天数选项
 */
export function generateMinDaysOptions() {
  // 选项为1-7天，数字+1递增
  return generateSalaryOptions(generateRange(1, 7, 1), '天')
}
