<template>
  <ElDialog
    v-model="visible"
    title="添加新地址"
    width="450px"
    :append-to-body="true"
    @open="handleOpen">
    <ElForm
      ref="formRef"
      label-position="top"
      :model="formData"
      :rules="rulesData">
      <ElFormItem
        label="工作城市"
        prop="code">
        <ElCascader
          v-model="formData.code"
          :options="cityOptions"
          :props="{
            label: 'name',
            value: 'code',
            emitPath: false,
          }"
          class="w-full" />
      </ElFormItem>
      <ElFormItem
        label="办公大楼"
        prop="officeBuilding">
        <ElSelect
          v-model="formData.officeBuilding"
          placeholder="请输入办公大楼"
          :filterable="true"
          :remote="true"
          :remote-method="getOfficeBuildingOptions">
          <ElOption
            v-for="item in officeBuildingOptions"
            :key="item.uid"
            :label="item.address"
            :value="item.address" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        label="详细地址"
        prop="detailAddress">
        <ElInput
          v-model="formData.detailAddress"
          placeholder="楼层/单元室/门牌号" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton
        type="primary"
        :loading="confirmLoading"
        @click="handleConfirm">
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { addWorkLocation, getWorkCityOptions, searchOfficeBuilding } from '@/apis/job'

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  function handleOpen() {
    getCityOptions()
  }

  const cityOptions = ref([])
  function getCityOptions() {
    getWorkCityOptions().then((data) => {
      cityOptions.value = data
    })
  }

  /**
   * 在树形结构中查找 code 对应的 name
   * @param {Array} tree 树形结构数组
   * @param {string} targetCode 要查找的 code
   * @returns {string|null} 找到的 name，未找到返回 null
   */
  function findNameByCode(tree, targetCode) {
    // 遍历树中的每个节点
    for (const node of tree) {
      // 如果当前节点的 code 匹配，返回 name
      if (node.code === targetCode) {
        return node.name
      }

      // 如果有子节点，递归查找子节点
      if (node.children && node.children.length > 0) {
        const result = findNameByCode(node.children, targetCode)
        if (result !== null) {
          return result
        }
      }
    }

    // 未找到返回 null
    return null
  }

  const officeBuildingOptions = ref([])
  function getOfficeBuildingOptions(query) {
    if (!query) {
      officeBuildingOptions.value = []
      return
    }
    if (!formData.code) {
      ElMessage.warning('请先选择工作城市')
      return
    }

    const city = findNameByCode(cityOptions.value, formData.code)
    console.log('city', city)
    if (!city) {
      ElMessage.warning('请先选择工作城市')
      return
    }

    searchOfficeBuilding({
      city: findNameByCode(cityOptions.value, formData.code),
      keyName: query,
    }).then((data) => {
      officeBuildingOptions.value = data
    })
  }

  const formRef = useTemplateRef('formRef')
  const formData = reactive({
    code: '',
    officeBuilding: '',
    detailAddress: '',
  })
  const rulesData = reactive({
    code: [{ required: true, message: '请选择工作城市', trigger: ['blur', 'change'] }],
    officeBuilding: [{ required: true, message: '请选择办公大楼', trigger: ['blur', 'change'] }],
    detailAddress: [{ required: true, message: '请输入详细地址', trigger: ['blur', 'change'] }],
  })

  function handleCancel() {
    visible.value = false
    formRef.value.resetFields()
  }

  const confirmLoading = ref(false)
  const emit = defineEmits(['confirm'])
  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        confirmLoading.value = true
        addWorkLocation({ ...formData, workCity: findNameByCode(cityOptions.value, formData.code) })
          .then(() => {
            ElMessage.success('添加成功')
            handleCancel()
            emit('confirm')
          })
          .finally(() => {
            confirmLoading.value = false
          })
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }
</script>

<style lang="scss" scoped></style>
