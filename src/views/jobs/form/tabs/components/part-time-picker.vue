<!-- 兼职时间选择器 -->
<template>
  <div class="w-full">
    <ElInput
      :value="showValue"
      @click="handleEdit(true)"></ElInput>

    <ElDialog
      v-model="dialogVisible"
      title="请选择兼职时间"
      width="655px"
      :append-to-body="true">
      <ElForm
        ref="formRef"
        label-position="left"
        label-width="110px"
        :model="formData"
        :rules="rulesData">
        <ElFormItem
          label="工作日期"
          prop="workDate">
          <div class="radio_group">
            <div
              v-for="item in workDateOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.workDate === item.value,
              }"
              @click="handleWorkDateClick(item.value)">
              {{ item.label }}
            </div>
            <div v-if="formData.workDate === '自定义'">
              <ElDatePicker
                v-model="formData.workDateCustom"
                style="width: 280px"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期" />
            </div>
            <div
              v-else
              class="radio_group_item"
              :class="{
                active: formData.workDate === '自定义',
              }"
              @click="handleWorkDateClick('自定义')">
              自定义
            </div>
          </div>
        </ElFormItem>
        <ElFormItem
          label="工作时间段"
          prop="workTimePeriod">
          <div class="radio_group">
            <div
              v-for="item in workTimePeriodOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.workTimePeriod === item.value,
              }"
              @click="handleWorkTimeClick(item.value)">
              {{ item.label }}
            </div>
          </div>
        </ElFormItem>
        <!-- 每周工作天数 -->
        <ElFormItem
          label="每周工作天数"
          prop="workDaysWeek">
          <div class="radio_group">
            <div
              v-for="item in minDaysOptions"
              :key="item.value"
              class="radio_group_item"
              :class="{
                active: formData.workDaysWeek === item.value,
              }"
              @click="handleMinDaysClick(item.value)">
              {{ item.label }}
            </div>
          </div>
        </ElFormItem>
        <!-- 工作时间 -->
        <ElFormItem
          label="工作时间"
          prop="workTime">
          <ElRadioGroup
            v-model="formData.workTime"
            @change="handleWorkTimeChange">
            <ElRadio
              v-for="item in workTimeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElRadioGroup>
          <template v-if="formData.workTime === '自定义'">
            <div class="mt-[12px] mb-[18px] w-full">
              <div
                v-for="(item, index) in formData.workTimeCustom"
                :key="index"
                class="mb-[8px]">
                <!-- <ElTimePicker
                  v-model="formData.workTimeCustom[index]"
                  style="width: 280px"
                  :is-range="true"
                  value-format="HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间" /> -->
                <ElTimeSelect
                  v-model="item[0]"
                  style="width: 120px"
                  format="HH:mm"
                  start="00:00"
                  step="00:15"
                  end="23:45"
                  :min-time="index > 0 ? formData.workTimeCustom[index - 1][1] : '00:00'"
                  :max-time="item[1]"
                  placeholder="开始时间" />
                <span class="mx-[8px]">至</span>
                <ElTimeSelect
                  v-model="item[1]"
                  style="width: 120px"
                  format="HH:mm"
                  start="00:00"
                  step="00:15"
                  end="23:45"
                  :min-time="item[0]"
                  :max-time="
                    index < formData.workTimeCustom.length - 1
                      ? formData.workTimeCustom[index + 1][0]
                      : '23:45'
                  "
                  placeholder="结束时间" />
                <ElButton
                  :circle="true"
                  class="ml-[8px]"
                  @click="handleRemoveWorkTime(index)">
                  <template #icon>
                    <i
                      class="iconfont icon-remove"
                      style="color: #606266; font-size: 12px" />
                  </template>
                </ElButton>
              </div>
              <ElButton
                v-if="formData.workTimeCustom.length < 3"
                type="primary"
                @click="handleAddWorkTime">
                <template #icon>
                  <i class="iconfont icon-circle-plus1" />
                </template>
                新增
              </ElButton>
            </div>
          </template>
          <template v-if="formData.workTime === '按班次'">
            <div class="radio_group mt-[12px]">
              <div
                v-for="item in workShiftOptions"
                :key="item.value"
                class="radio_group_item"
                :class="{
                  active: formData.workShift.includes(item.value),
                }"
                @click="handleWorkShiftClick(item.value)">
                {{ item.label }}
              </div>
            </div>
          </template>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs'
  import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
  import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

  dayjs.extend(isSameOrBefore)
  dayjs.extend(isSameOrAfter)

  const workDate = defineModel('workDate', {
    type: String,
    default: '',
  })

  const workTimePeriod = defineModel('workTimePeriod', {
    type: String,
    default: '',
  })

  const workDaysWeek = defineModel('workDaysWeek', {
    type: String,
    default: '',
  })

  const workTime = defineModel('workTime', {
    type: String,
    default: '',
  })

  const showValue = computed(() => {
    const workDateLabel =
      workDateOptions.find((item) => workDate.value.includes(item.value))?.label ||
      (workDate.value.includes('|') ? '自定义' : '')

    const workTimePeriodLabel = workTimePeriodOptions.find(
      (item) => item.value === workTimePeriod.value,
    )?.label

    const workDaysWeekLabel = minDaysOptions.find(
      (item) => item.value === workDaysWeek.value,
    )?.label

    const workTimeLabel =
      workTimeOptions.find((item) => workTime.value.includes(item.value))?.label || ''

    return workDateLabel && workTimePeriodLabel && workDaysWeekLabel && workTimeLabel
      ? `工作日期：${workDateLabel}；` +
          `工作时间段：${workTimePeriodLabel}；` +
          `每周工作天数：${workDaysWeekLabel}；` +
          `工作时间：${workTimeLabel}；`
      : ''
  })

  const dialogVisible = ref(false)
  function handleEdit(visible = true) {
    dialogVisible.value = visible

    if (workDate.value.includes('|')) {
      formData.workDate = '自定义'
      formData.workDateCustom = workDate.value.split('|')[1].split(',')
    } else {
      formData.workDate = workDate.value
    }

    formData.workTimePeriod = workTimePeriod.value
    formData.workDaysWeek = workDaysWeek.value

    if (workTime.value.includes('|')) {
      formData.workTime = workTime.value.split('|')[0]
      if (formData.workTime === '自定义') {
        formData.workTimeCustom = workTime.value
          .split('|')[1]
          .split(',')
          .map((item) => item.split('-'))
        formData.workShift = []
      } else if (formData.workTime === '按班次') {
        formData.workShift = workTime.value.split('|')[1].split(',')
        formData.workTimeCustom = []
      }
    } else {
      formData.workTime = workTime.value
      formData.workTimeCustom = []
      formData.workShift = []
    }
  }

  const formRef = useTemplateRef('formRef')
  const formData = reactive({
    workDate: '', // 工作日期
    workDateCustom: [], // 工作日期自定义
    workTimePeriod: '', // 工作时间段
    workDaysWeek: '', // 每周工作天数
    workTime: '', // 工作时间
    workTimeCustom: [], // 工作时间自定义
    workShift: [], // 工作班次
  })
  const rulesData = reactive({
    workDate: [
      { required: true, message: '请选择工作日期', trigger: ['blur', 'change'] },
      {
        validator: (rule, value, cb) => {
          if (!formData.workDate) {
            cb(new Error('请选择工作日期'))
          }
          if (formData.workDate === '自定义' && !formData.workDateCustom?.length) {
            cb(new Error('请选择自定义工作日期'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    workTimePeriod: [{ required: true, message: '请选择工作时间段', trigger: ['blur', 'change'] }],
    workDaysWeek: [{ required: true, message: '请选择每周工作天数', trigger: ['blur', 'change'] }],
    workTime: [
      { required: true, message: '请选择工作时间', trigger: ['blur', 'change'] },
      {
        validator: (rule, value, cb) => {
          if (!formData.workTime) {
            cb(new Error('请选择工作时间'))
          }
          if (formData.workTime === '自定义') {
            const validate1 =
              formData.workTimeCustom.length &&
              formData.workTimeCustom.every((item) => item.length === 2 && item[0] && item[1])

            // 时间段不能重复
            const validate2 = formData.workTimeCustom.every((item, index) => {
              return !formData.workTimeCustom.slice(index + 1).some((item2) => {
                return (
                  // 当前开始时间在比较的时间段内
                  (dayjs(item[0], 'HH:mm').isSameOrAfter(dayjs(item2[0], 'HH:mm')) &&
                    dayjs(item[0], 'HH:mm').isSameOrBefore(dayjs(item2[1], 'HH:mm'))) ||
                  // 当前结束时间在比较的时间段内
                  (dayjs(item[1], 'HH:mm').isSameOrAfter(dayjs(item2[0], 'HH:mm')) &&
                    dayjs(item[1], 'HH:mm').isSameOrBefore(dayjs(item2[1], 'HH:mm'))) ||
                  // 当前时间段包含比较的时间段
                  (dayjs(item2[0], 'HH:mm').isSameOrAfter(dayjs(item[0], 'HH:mm')) &&
                    dayjs(item2[1], 'HH:mm').isSameOrBefore(dayjs(item[1], 'HH:mm')))
                )
              })
            })
            if (!validate1) {
              cb(new Error('请选择自定义工作时间'))
            } else if (!validate2) {
              cb(new Error('自定义工作时间不能重复'))
            } else {
              cb()
            }
          }
          if (formData.workTime === '按班次') {
            if (!formData.workShift?.length) {
              cb(new Error('请选择工作班次'))
            } else {
              cb()
            }
          }
          if (formData.workTime === '不限时间') {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
  })

  const workDateOptions = [
    { label: '1个月', value: '1个月' },
    { label: '2个月', value: '2个月' },
    { label: '3个月', value: '3个月' },
    { label: '4个月', value: '4个月' },
    { label: '长期兼职', value: '长期兼职' },
  ]
  function handleWorkDateClick(value) {
    formData.workDate = value
    formData.workDateCustom = []
    formRef.value.validateField('workDate')
  }

  const workTimePeriodOptions = [
    { label: '工作日', value: '工作日' },
    { label: '周末节假日', value: '周末节假日' },
    { label: '全周轮班', value: '全周轮班' },
    { label: '按单安排时间', value: '按单安排时间' },
    { label: '不限时间', value: '不限时间' },
  ]
  function handleWorkTimeClick(value) {
    formData.workTimePeriod = value
    formRef.value.validateField('workTimePeriod')
  }

  const minDaysOptions = [
    { label: '5天及以上', value: '5天及以上' },
    { label: '3-4天', value: '3-4天' },
    { label: '2-3天', value: '2-3天' },
    { label: '1-2天', value: '1-2天' },
    { label: '无要求', value: '无要求' },
  ]
  function handleMinDaysClick(value) {
    formData.workDaysWeek = value
    formRef.value.validateField('workDaysWeek')
  }

  function handleWorkTimeChange(value) {
    if (value === '自定义') {
      formData.workTimeCustom = []
    } else if (value === '按班次') {
      formData.workShift = []
    }
    formRef.value.validateField('workTime')
  }

  const workTimeOptions = [
    { label: '自定义', value: '自定义' },
    { label: '按班次', value: '按班次' },
    { label: '不限时间', value: '不限时间' },
  ]

  function handleAddWorkTime() {
    if (formData.workTimeCustom.length >= 3) {
      return
    }
    formData.workTimeCustom.push([null, null])
    formRef.value.validateField('workTime')
  }

  function handleRemoveWorkTime(index) {
    formData.workTimeCustom.splice(index, 1)
    formRef.value.validateField('workTime')
  }

  const workShiftOptions = [
    { label: '早班', value: '早班' },
    { label: '午班', value: '午班' },
    { label: '晚班', value: '晚班' },
    { label: '夜班', value: '夜班' },
  ]
  // 多选
  function handleWorkShiftClick(value) {
    if (formData.workShift.includes(value)) {
      formData.workShift = formData.workShift.filter((item) => item !== value)
    } else {
      formData.workShift.push(value)
    }
    formRef.value.validateField('workTime')
  }

  function handleCancel() {
    dialogVisible.value = false
  }

  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        // 自定义|2023-09-01,2023-09-07
        workDate.value =
          formData.workDate +
          (formData.workDate === '自定义' ? '|' + formData.workDateCustom.join(',') : '')
        workTimePeriod.value = formData.workTimePeriod
        workDaysWeek.value = formData.workDaysWeek
        // 自定义|09:00:00-12:00:00,13:00:00-17:00:00,18:00:00-21:00:00
        // 按班次|早班,午班,晚班
        // 不限时间
        workTime.value =
          formData.workTime +
          (formData.workTime === '自定义'
            ? '|' + formData.workTimeCustom.map((item) => item.join('-')).join(',')
            : '') +
          (formData.workTime === '按班次' ? '|' + formData.workShift.join(',') : '')

        dialogVisible.value = false
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }

  defineExpose({
    validate() {
      return new Promise((resolve, reject) => {
        if (!showValue.value) {
          reject(new Error('请先完善兼职时间'))
          return
        }

        if (!formRef.value) {
          // 这里先初始化表单，但不显示
          handleEdit(false)
          // 自己实现一个简易的 rulesData 校验
          const validationPromises = []

          for (const key in rulesData) {
            const rule = rulesData[key]
            const value = formData[key]

            for (const item of rule) {
              if (item.required && !value) {
                reject(new Error('请先完善兼职时间'))
                return
              }

              if (item.validator) {
                validationPromises.push(
                  new Promise((resolveValidator, rejectValidator) => {
                    item.validator(null, value, (err) => {
                      if (err) {
                        rejectValidator(err)
                      } else {
                        resolveValidator()
                      }
                    })
                  }),
                )
              }
            }
          }

          if (validationPromises.length > 0) {
            Promise.all(validationPromises)
              .then(() => resolve())
              .catch((err) => reject(err))
          } else {
            resolve()
          }
          return
        }

        formRef.value.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('请先完善兼职时间'))
          }
        })
      })
    },
  })
</script>

<style lang="scss" scoped>
  .radio_group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .radio_group_item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 116px;
      height: 32px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #ffffff;
      color: #606266;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        border-color: #c0c4cc;
      }

      &.active {
        border: 1px solid #79bbff;
        background: #0055ff;
        color: #ffffff;
      }
    }
  }
</style>
