<template>
  <ElTabPane
    class="h-full w-full overflow-auto"
    label="基础信息"
    name="基础信息">
    <div
      v-if="formType"
      class="mx-auto my-[5px] w-[1030px]">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData"
        :rules="rulesData">
        <ElFormItem
          label="职位类型"
          prop="positionType">
          <ElRadioGroup
            v-model="formData.positionType"
            :disabled="formType === 'edit'">
            <ElRadio
              v-for="item in JOB_TYPE"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :border="true" />
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem
          label="职位名称"
          prop="extraData">
          <ElInput
            v-model="formData.extraData"
            placeholder="请输入职位名称"
            :minlength="1"
            :maxlength="20"
            :show-word-limit="true" />
        </ElFormItem>
        <ElFormItem
          label="职位描述"
          prop="positionDesc">
          <ElInput
            v-model="formData.positionDesc"
            type="textarea"
            :rows="4"
            placeholder="请勿填写QQ、微信、电话等联系方式及特殊符号、性别歧视词、违反劳动法相关内容"
            :show-word-limit="true"
            :maxlength="5000"
            :autosize="{ minRows: 4, maxRows: 8 }"
            resize="none" />
          <AiGen
            :position-type="formData.positionType"
            :position-name="formData.positionName"
            @confirm="
              (content) => {
                formData.positionDesc = content
                formData.aiGenerateJD = true
              }
            " />
        </ElFormItem>
        <ElFormItem
          label="职位分类"
          prop="positionName">
          <ElSelect
            v-model="formData.positionName"
            placeholder="请输入职位名称"
            :disabled="formType === 'edit'"
            :filterable="true"
            :remote="true"
            :remote-method="getPositionNameOptions">
            <ElOption
              v-for="item in positionNameOptions"
              :key="item.positionName"
              :label="item.positionName"
              :value="item.positionName" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="职位关键词"
          prop="keywordsArray">
          <AiButton
            :loading="generateKeywordsLoading"
            :disabled="!formData.positionName || !formData.positionDesc"
            size="small"
            @click="handleGenKeywords">
            生成关键词
          </AiButton>
          <div class="mt-[8px] flex w-full flex-wrap gap-[8px]">
            <ElTag
              v-for="item in keywordsArray"
              :key="item"
              :closable="item.type === 'custom'"
              :disable-transitions="true"
              @close="handleCloseTag(item)">
              {{ item.label }}
            </ElTag>
          </div>
        </ElFormItem>
        <ElRow :gutter="32">
          <ElCol :span="12">
            <ElFormItem
              label="经验"
              prop="experience">
              <ElSelect
                v-model="formData.experience"
                :disabled="
                  formType === 'edit' &&
                  (formData.positionType === '应届校园招聘' ||
                    formData.positionType === '实习生招聘')
                "
                placeholder="请选择经验">
                <!-- 社招、兼职 -->
                <template
                  v-if="
                    formData.positionType === '社招全职' || formData.positionType === '兼职招聘'
                  ">
                  <ElOption
                    label="不限"
                    value="不限" />
                  <ElOption
                    v-for="item in JOB_YEAR"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </template>
                <!-- 校招、实习 直接显示在校/应届 -->
                <template v-else>
                  <ElOption
                    label="在校/应届"
                    value="在校/应届" />
                </template>
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="学历"
              prop="qualification">
              <ElSelect
                v-model="formData.qualification"
                placeholder="请选择学历">
                <ElOption
                  label="不限"
                  value="不限" />
                <ElOption
                  v-for="item in JOB_EDUCATION"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <!-- 招聘类型为兼职时显示 -->
          <ElCol
            v-if="formData.positionType === '兼职招聘'"
            :span="12">
            <ElFormItem
              label="结算方式"
              prop="paymentMethod">
              <ElSelect v-model="formData.paymentMethod">
                <ElOption
                  v-for="item in JOB_SETTLEMENT_METHOD"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="薪资范围"
              prop="salaryRange">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.minSalary"
                  class="flex-1">
                  <ElOption
                    v-for="item in salaryRange.min"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.maxSalary"
                  class="flex-1">
                  <ElOption
                    v-for="item in salaryRange.max"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <!-- 实习 -->
                <span
                  v-if="formData.positionType === '实习生招聘'"
                  class="ml-[8px] flex-1 text-[14px] text-[#303133]">
                  元/天
                </span>
                <!-- 社招、校招、兼职 -->
                <template v-else>
                  <span class="mx-[8px]">x</span>
                  <ElSelect
                    v-model="formData.salaryStandards"
                    class="flex-1">
                    <ElOption
                      v-for="item in salaryRange.standard"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </ElSelect>
                </template>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 社招且岗位为销售性质 -->
          <ElCol
            v-if="formData.positionType === '社招全职' && isSales"
            :span="12">
            <ElFormItem
              label="底薪"
              prop="basicSalary">
              <div class="flex items-center">
                <ElSelect
                  v-model="formData.basicSalary"
                  style="width: 150px; margin-right: 8px">
                  <ElOption
                    v-for="item in generateBaseSalaryOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <ElRadio :disabled="true">固定</ElRadio>
              </div>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="工作地点"
              prop="addressBid">
              <AddressPicker v-model:address-bid="formData.addressBid" />
            </ElFormItem>
          </ElCol>
          <!-- 校招时显示 -->
          <ElCol
            v-if="formData.positionType === '应届校园招聘'"
            :span="12">
            <ElFormItem
              label="毕业时间"
              prop="graduationTime">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.minGradeTime"
                  class="flex-1">
                  <ElOption
                    label="不限"
                    value="不限" />
                  <ElOption
                    v-for="item in generateGraduationTimeOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.maxGradeTime"
                  class="flex-1">
                  <ElOption
                    v-if="formData.minGradeTime === '不限'"
                    label="不限"
                    value="不限" />
                  <template v-else-if="formData.minGradeTime">
                    <!-- 选项为选择的开始时间 和 开始时间 + 1 -->
                    <ElOption
                      :label="`${formData.minGradeTime}年`"
                      :value="formData.minGradeTime" />
                    <ElOption
                      :label="`${formData.minGradeTime + 1}年`"
                      :value="formData.minGradeTime + 1" />
                  </template>
                </ElSelect>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 实习时显示 -->
          <ElCol
            v-if="formData.positionType === '实习生招聘'"
            :span="12">
            <ElFormItem
              label="实习要求"
              prop="internshipRequirements">
              <div class="flex w-full items-center">
                <ElSelect
                  v-model="formData.minInternshipMonths"
                  class="flex-1"
                  placeholder="最少实习月数">
                  <ElOption
                    v-for="item in generateMinInternshipOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
                <span class="mx-[8px]">-</span>
                <ElSelect
                  v-model="formData.minWeekNums"
                  class="flex-1"
                  placeholder="最少周到岗天数">
                  <ElOption
                    v-for="item in generateMinDaysOptions()"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </div>
            </ElFormItem>
          </ElCol>
          <!-- 兼职时显示 -->
          <ElCol
            v-if="formData.positionType === '兼职招聘'"
            :span="12">
            <ElFormItem
              label="兼职时间"
              prop="partTime">
              <PartTimePicker
                ref="partTimePickerRef"
                v-model:work-date="formData.workDate"
                v-model:work-time-period="formData.workTimePeriod"
                v-model:work-days-week="formData.workDaysWeek"
                v-model:work-time="formData.workTime" />
            </ElFormItem>
          </ElCol>
          <!-- 校招、兼职时显示 -->
          <ElCol
            v-if="formData.positionType === '应届校园招聘' || formData.positionType === '兼职招聘'"
            :span="12">
            <ElFormItem
              label="招聘截止时间"
              prop="recruitDeadline">
              <ElDatePicker
                v-model="formData.recruitDeadline"
                class="flex-1"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择招聘截止时间" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="所属部门"
              prop="deptBid">
              <ElCascader
                v-model="formData.deptBid"
                :options="deptOptions"
                :props="{
                  label: 'name',
                  value: 'id',
                  emitPath: false,
                }"
                class="w-full" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
  </ElTabPane>
</template>

<script setup>
  import { AiButton } from '@/components'
  import { useDict } from '@/stores/dict'
  import AddressPicker from './components/address-picker/index.vue'
  import AiGen from './components/ai-gen.vue'
  import PartTimePicker from './components/part-time-picker.vue'
  import {
    generateBaseSalaryOptions,
    generateGraduationTimeOptions,
    generateMinDaysOptions,
    generateMinInternshipOptions,
    getSalaryRange,
    processAiResponse,
  } from './utils.js'
  import {
    generateJobDescAndKeywords,
    getTenantKnowledgeBase,
    getDeptList,
    getJobNameOptions,
  } from '@/apis/job'
  import useUserStore from '@/stores/user'

  const formData = defineModel('formData', {
    type: Object,
  })

  const { formType, shouldResetFields } = defineProps({
    formType: {
      type: String,
      default: '',
      validator: (value) => ['create', 'edit'].includes(value),
    },
    shouldResetFields: {
      type: Boolean,
      default: false,
    },
  })

  const { JOB_TYPE, JOB_YEAR, JOB_EDUCATION, JOB_SETTLEMENT_METHOD } = useDict(
    'JOB_TYPE',
    'JOB_YEAR',
    'JOB_EDUCATION',
    'JOB_SETTLEMENT_METHOD',
  )
  const formRef = useTemplateRef('formRef')
  const partTimePickerRef = useTemplateRef('partTimePickerRef')
  const rulesData = reactive({
    positionType: [{ required: true, message: '请选择职位类型', trigger: ['blur', 'change'] }],
    extraData: [
      { required: true, message: '请输入职位名称', trigger: ['blur', 'change'] },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\uff00-\uffef\x21-\x7e]{1,20}$/,
        message: '文件夹名称仅支持中文、英文、数字、符号（除空格）且长度在1-20个字符之间',
        trigger: ['blur', 'change'],
      },
    ],
    positionName: [{ required: true, message: '请输入职位分类', trigger: ['blur', 'change'] }],
    positionDesc: [
      { required: true, message: '请输入职位描述', trigger: ['blur', 'change'] },
      {
        validator: (rule, value, cb) => {
          // 计算有多少个字符，汉字算2，英文算1
          const length = value.split('').reduce((acc, cur) => {
            return acc + (cur.charCodeAt(0) > 255 ? 2 : 1)
          }, 0)
          if (length < 4) {
            cb(new Error('职位描述不能少于2个汉字或4个英文字符'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    keywordsArray: [
      {
        validator: (rule, value, cb) => {
          if (!formData.value.customKeywords?.length) {
            cb(new Error('请生成职位关键词'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    experience: [{ required: true, message: '请选择经验', trigger: ['blur', 'change'] }],
    qualification: [{ required: true, message: '请选择学历', trigger: ['blur', 'change'] }],
    paymentMethod: [{ required: true, message: '请选择结算方式', trigger: ['blur', 'change'] }],
    salaryRange: [
      {
        validator: (rule, value, cb) => {
          if (
            !formData.value.minSalary || // 没选择薪资范围
            !formData.value.maxSalary || // 没选择薪资范围
            formData.value.minSalary > formData.value.maxSalary || // 最低薪资大于最高薪资
            !formData.value.salaryStandards // 没选择发放单位
          ) {
            cb(new Error('请选择薪资范围'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    basicSalary: [{ required: true, message: '请选择底薪', trigger: ['blur', 'change'] }],
    addressBid: [{ required: true, message: '请选择工作地点', trigger: ['blur', 'change'] }],
    graduationTime: [
      {
        validator: (rule, value, cb) => {
          if (!formData.value.minGradeTime || !formData.value.maxGradeTime) {
            cb(new Error('请选择毕业时间'))
            return
          }
          if (formData.value.minGradeTime === '不限' || formData.value.maxGradeTime === '不限') {
            cb()
            return
          }
          // 开始时间大于结束时间
          if (formData.value.minGradeTime > formData.value.maxGradeTime) {
            cb(new Error('请选择毕业时间'))
            return
          }
          cb()
        },
        trigger: ['blur', 'change'],
      },
    ],
    internshipRequirements: [
      {
        validator: (rule, value, cb) => {
          if (!formData.value.minInternshipMonths || !formData.value.minWeekNums) {
            cb(new Error('请选择实习要求'))
          } else {
            cb()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    partTime: [
      {
        validator: (rule, value, cb) => {
          partTimePickerRef.value.validate().then(cb).catch(cb)
        },
        trigger: ['blur', 'change'],
      },
    ],
    recruitDeadline: [
      { required: true, message: '请选择招聘截止时间', trigger: ['blur', 'change'] },
    ],
    deptBid: [{ required: true, message: '请选择所属部门', trigger: ['blur', 'change'] }],
  })

  // 职位名称远程搜索
  const positionNameOptions = ref([])
  // 职位是否为销售性质
  const isSales = computed(() => {
    return positionNameOptions.value.some(
      (item) => item.positionName === formData.value.positionName && item.basicSalary,
    )
  })
  // 薪资单位
  const salaryUnit = computed(() => {
    return (
      positionNameOptions.value.find((item) => item.positionName === formData.value.positionName)
        ?.salaryUnit || ''
    )
  })
  function getPositionNameOptions(query) {
    if (!query) {
      positionNameOptions.value = []
      return
    }
    getJobNameOptions({
      positionType: formData.value.positionType,
      positionName: query,
    }).then((data) => {
      positionNameOptions.value = data
    })
  }
  watch(
    () => formData.value.positionName,
    () => {
      if (formType === 'edit' && formData.value.positionName) {
        getPositionNameOptions(formData.value.positionName)
      }
    },
  )

  // 生成关键词
  const keywordsArray = computed(() => {
    const result = []

    // 将必选关键词添加到结果数组
    if (formData.value.requiredKeywords?.length) {
      result.push(...formData.value.requiredKeywords.map((label) => ({ label, type: 'required' })))
    }

    // 将自定义关键词添加到结果数组
    if (formData.value.customKeywords?.length) {
      result.push(...formData.value.customKeywords.map((label) => ({ label, type: 'custom' })))
    }

    return result
  })

  const useStore = useUserStore()
  const generateKeywordsLoading = ref(false)
  async function handleGenKeywords() {
    generateKeywordsLoading.value = true

    formData.value.requiredLabel = ''
    formData.value.requiredKeywords = []
    formData.value.customKeywords = []

    try {
      const datasetId = await getTenantKnowledgeBase()

      const res = await generateJobDescAndKeywords({
        option: '2',
        job_type: formData.value.positionType,
        position_name: formData.value.positionName,
        position_description: formData.value.positionDesc,
        phone_num: useStore.phone,
        datasetId: datasetId,
      })

      const { custom_label, select_label, required_label } = processAiResponse(res)

      formData.value.requiredLabel = JSON.stringify(required_label)
      // 去重后赋值
      formData.value.requiredKeywords = [...select_label]
      formData.value.customKeywords = [...custom_label]

      formData.value.aiGenerateJD = true
    } catch (err) {
      console.log(err)
      ElMessage.error('生成失败')
    } finally {
      generateKeywordsLoading.value = false
    }
  }
  function handleCloseTag(item) {
    // 如果没有固定关键词，要保留至少一个自定义关键词
    if (!formData.value.requiredKeywords?.length && formData.value.customKeywords.length === 1) {
      ElMessage.warning('没有固定关键词时，至少保留一个自定义关键词')
      return
    }
    formData.value.customKeywords = formData.value.customKeywords.filter((i) => {
      return i !== item.label
    })
  }

  // 根据职位类型、结算方式、发放单位，计算薪资范围
  const salaryRange = computed(() => {
    return getSalaryRange(formData.value.positionType, {
      method: formData.value.paymentMethod,
      unit: formData.value.unit,
      standard: formData.value.salaryStandards,
    })
  })
  // 薪资单位改变，重置薪资范围
  watch(salaryUnit, () => {
    formData.value.unit = salaryUnit.value
  })

  const deptOptions = ref([])
  function getDeptOptions() {
    getDeptList().then((data) => {
      deptOptions.value = data
    })
  }
  onMounted(() => {
    getDeptOptions()
  })

  // 重置表单字段的函数
  function resetFormFields(fields) {
    Object.entries(fields).forEach(([key, value]) => {
      formData.value[key] = value
      if (key === 'unit') {
        formData.value[key] = salaryUnit.value || value
      }
    })
  }

  // 职位类型对应的默认字段值
  const positionTypeDefaults = computed(() => {
    return {
      社招全职: {
        experience: '不限',
        paymentMethod: '',
        minSalary: 1,
        maxSalary: 2,
        unit: 'K',
        salaryStandards: '12薪',
        basicSalary: 1000,
        minGradeTime: '',
        maxGradeTime: '',
        minInternshipMonths: '',
        minWeekNums: '',
        workDate: '',
        workTimePeriod: '',
        workDaysWeek: '',
        workTime: '',
        recruitDeadline: '',
      },
      应届校园招聘: {
        experience: '在校/应届',
        paymentMethod: '',
        minSalary: 1,
        maxSalary: 2,
        unit: 'K',
        salaryStandards: '12薪',
        basicSalary: '',
        minGradeTime: '不限',
        minInternshipMonths: '',
        minWeekNums: '',
        workDate: '',
        workTimePeriod: '',
        workDaysWeek: '',
        workTime: '',
        recruitDeadline: '',
      },
      兼职招聘: {
        experience: '不限',
        paymentMethod: JOB_SETTLEMENT_METHOD.value[0]?.value,
        unit: '元',
        basicSalary: '',
        minGradeTime: '',
        maxGradeTime: '',
        minInternshipMonths: '',
        minWeekNums: '',
        workDate: '',
        workTimePeriod: '',
        workDaysWeek: '',
        workTime: '',
        recruitDeadline: '',
      },
      实习生招聘: {
        experience: '在校/应届',
        paymentMethod: '',
        minSalary: 10,
        maxSalary: 20,
        unit: '元',
        salaryStandards: '天',
        basicSalary: '',
        minGradeTime: '',
        maxGradeTime: '',
        minInternshipMonths: 1,
        minWeekNums: 1,
        workDate: '',
        workTimePeriod: '',
        workDaysWeek: '',
        workTime: '',
        recruitDeadline: '',
      },
    }
  })
  // 职位类型改变，重置相关字段
  watch(
    () => formData.value.positionType,
    () => {
      if (!shouldResetFields) {
        return
      }

      const positionType = formData.value.positionType
      const defaults = positionTypeDefaults.value[positionType]
      console.log('职位类型改变，重置相关字段', defaults)
      if (defaults) {
        resetFormFields(defaults)
      }
    },
    { immediate: true },
  )

  // 结算方式对应的默认值
  const paymentMethodDefaults = {
    日结: { salaryStandards: '时' },
    周结: { minSalary: 100, maxSalary: 200, salaryStandards: '周' },
    月结: { minSalary: 500, maxSalary: 1000, salaryStandards: '月' },
  }

  watch(
    () => formData.value.paymentMethod,
    () => {
      if (!shouldResetFields) {
        return
      }

      const paymentMethod = formData.value.paymentMethod
      const defaults = paymentMethodDefaults[paymentMethod]
      if (defaults) {
        resetFormFields(defaults)
      }
    },
  )

  watch(
    () => formData.value.salaryStandards,
    () => {
      if (!shouldResetFields) {
        return
      }

      const salaryStandards = formData.value.salaryStandards
      if (salaryStandards === '时') {
        resetFormFields({ minSalary: 5, maxSalary: 10 })
      }
      if (salaryStandards === '天' && formData.value.positionType === '兼职招聘') {
        resetFormFields({ minSalary: 50, maxSalary: 60 })
      }
    },
  )

  // 毕业时间改变，重置相关字段
  watch(
    () => formData.value.minGradeTime,
    () => {
      if (!shouldResetFields) {
        return
      }

      const minGradeTime = formData.value.minGradeTime
      resetFormFields({
        maxGradeTime: minGradeTime === '不限' ? '不限' : minGradeTime,
      })
    },
  )

  defineExpose({
    validate() {
      return new Promise((resolve, reject) => {
        formRef.value.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('请先完善基础信息'))
          }
        })
      })
    },
  })
</script>

<style lang="scss" scoped></style>
