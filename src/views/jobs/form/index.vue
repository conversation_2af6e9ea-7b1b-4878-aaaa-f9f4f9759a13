<template>
  <div class="form">
    <PageHeader :content="pageTitle">
      <template #extra>
        <ElButton
          type="primary"
          :plain="true"
          :loading="saveLoading"
          @click="handleSave(false)">
          保存草稿
        </ElButton>
        <ElButton
          type="primary"
          :loading="publishLoading"
          @click="handleSave(true)">
          发布职位
        </ElButton>
      </template>
    </PageHeader>
    <div class="form_content">
      <ElTabs
        v-model="activeTab"
        style="height: 100%"
        type="border-card">
        <BasicInfo
          ref="basicInfoRef"
          v-model:form-data="formData"
          :form-type="formType"
          :should-reset-fields="shouldResetFields" />
        <ScoreWeight
          ref="scoreWeightRef"
          v-model:form-data="formData"
          :form-type="formType" />
      </ElTabs>
    </div>
  </div>
  <FbqdSelectDialog
    v-model="fbqdSelectDialogOptions.visible"
    :loading="fbqdSelectDialogOptions.loading"
    @confirm="handleFbqdSelectDialogConfirm" />
</template>

<script setup>
  import { getJobDetail, publishJob, saveJob } from '@/apis/job'
  import { PageHeader } from '@/components'
  import FbqdSelectDialog from '../components/fbqd-select-dialog.vue'
  import BasicInfo from './tabs/basic-info.vue'
  import ScoreWeight from './tabs/score-weight.vue'
  const route = useRoute()
  const router = useRouter()

  // 激活的标签页
  const activeTab = ref('基础信息')

  // 表单类型 create/edit
  const formType = computed(() => route.meta.formType)

  // 页面标题
  const pageTitle = computed(() => {
    return formType.value === 'create' ? '新增职位' : '编辑职位'
  })

  const jobId = computed(() => {
    return route.query.jobId || ''
  })

  const shouldResetFields = ref(false)
  function initFormData() {
    if (formType.value === 'edit' && jobId.value) {
      getJobDetail({ bid: jobId.value }).then((data) => {
        for (const key in formData) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            if (key === 'requiredKeywords') {
              formData.requiredKeywords = data.requiredKeywords || []
            } else if (key === 'customKeywords') {
              formData.customKeywords = data.customKeywords || []
            } else {
              formData[key] = data[key] || ''
            }
          }
        }
        nextTick(() => {
          shouldResetFields.value = true
        })
      })
    } else {
      shouldResetFields.value = true
    }
  }
  onMounted(() => {
    initFormData()
  })

  const formData = reactive({
    // 基础信息
    positionType: '社招全职', // 职位类型：社招全职、应届校园招聘、兼职招聘、实习生招聘
    extraData: '', // 新的职位名称
    positionName: '', // 职位分类，原职位名称
    positionDesc: '', // 职位描述
    requiredLabel: '', // 职位关键词：保存给 python 端使用
    requiredKeywords: [], // 职位固定关键词：保存给前端使用，必填项不可删除
    customKeywords: [], // 职位自定义关键词：保存给前端使用，可删除
    experience: '不限', // 经验：1年以内、1-3年、3-5年、5-10年、10年以上
    qualification: '不限', // 学历：初中及以下、中专/中技、高中、大专、本科、硕士、博士
    paymentMethod: '', // 结算方式
    minSalary: '', // 薪资范围-下限
    maxSalary: '', // 薪资范围-上限
    unit: 'K', // 薪资范围-单位
    salaryStandards: '', // 薪资范围-单位
    basicSalary: '', // 底薪
    addressBid: '', // 工作地点
    minGradeTime: '', // 毕业时间
    maxGradeTime: '', // 毕业时间
    minInternshipMonths: '', // 最少实习月数
    minWeekNums: '', // 周到岗天数
    workDate: '', // 工作日期
    workTimePeriod: '', // 工作时间段
    workDaysWeek: '', // 每周工作天数
    workTime: '', // 工作时间
    recruitDeadline: '', // 招聘截止时间
    deptBid: '', // 所属部门
    // 评分权重
    eduWeight: 30, // 教育背景
    workingAbilityWeight: 50, // 工作能力
    languageAbilityWeight: 5, // 语言能力
    awardWeight: 5, // 荣誉奖项
    skillsWeight: 10, // 技能证书
    // AI 生成
    aiGenerateJD: false, // 记录是否点击过 AI 生成职位描述 或 生成关键词
  })

  const basicInfoRef = useTemplateRef('basicInfoRef')
  const scoreWeightRef = useTemplateRef('scoreWeightRef')

  const saveLoading = ref(false)
  const publishLoading = ref(false)

  const fbqdSelectDialogOptions = reactive({
    visible: false,
    loading: false,
    data: {},
  })

  function handleSave(isPublish) {
    if (isPublish) {
      Promise.all([basicInfoRef.value.validate(), scoreWeightRef.value.validate()])
        .then(() => {
          console.log('保存成功', formData)
          fbqdSelectDialogOptions.visible = true
        })
        .catch((e) => {
          ElMessage.error(e.message)
        })
    } else {
      console.log('保存草稿', formData)
      saveLoading.value = true
      saveJob({
        ...formData,
        bid: jobId.value ? jobId.value : undefined,
      })
        .then(() => {
          ElMessage.success('保存成功')
          router.push('/jobs')
        })
        .finally(() => {
          saveLoading.value = false
        })
    }
  }
  function handleFbqdSelectDialogConfirm(releaseChannel) {
    fbqdSelectDialogOptions.loading = true
    publishJob({
      ...formData,
      bid: jobId.value ? jobId.value : undefined,
      releaseChannel,
    })
      .then(() => {
        // ElMessage.success('发布成功')
        ElMessage.success('职位发布中...')
        router.push('/jobs')
      })
      .finally(() => {
        fbqdSelectDialogOptions.loading = false
      })
  }
</script>

<style lang="scss" scoped>
  .form {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
    }
  }
</style>
