<template>
  <div class="job-detail">
    <ElAlert
      v-if="detailData.status === '发布失败'"
      title="失败原因"
      :description="detailData.failureReason || '暂无失败原因'"
      type="error"
      :show-icon="true"
      :closable="false" />

    <DescriptionItems
      title="基础信息"
      :items="basicInfoItems"
      :data="detailData" />

    <div class="mt-[40px]">
      <DescriptionItems
        title="评分权重"
        :items="scoreWeightItems"
        :data="detailData" />
    </div>

    <div
      v-if="showWatermark"
      class="job-detail_watermark">
      <!-- 已发布 -->
      <i
        v-if="jobStatus === '已发布'"
        class="iconfont icon-Published"
        style="color: #95d475" />
      <!-- 已关闭 -->
      <i
        v-if="jobStatus === '已关闭'"
        class="iconfont icon-CLOSED"
        style="color: #b1b3b8" />
      <!-- 发布失败 -->
      <i
        v-if="jobStatus === '发布失败'"
        class="iconfont icon-a-Publicationfailed"
        style="color: #f89898" />
      <!-- 草稿 -->
      <i
        v-if="jobStatus === '草稿'"
        class="iconfont icon-draft"
        style="color: #79bbff" />
    </div>
  </div>
</template>

<script setup>
  import { getJobDetail, getJobNameOptions } from '@/apis/job'
  import DescriptionItems from './description-items.vue'

  const { jobId, showWatermark } = defineProps({
    jobId: {
      type: String,
      default: '',
    },
    showWatermark: {
      type: Boolean,
      default: true,
    },
  })

  const emits = defineEmits(['data'])

  // 详情数据
  const detailData = ref({})
  // 获取详情数据
  function getDetailData() {
    if (!jobId) return

    getJobDetail({ bid: jobId }).then((data) => {
      detailData.value = data
      emits('data', data)
    })
  }

  onMounted(() => {
    getDetailData()
  })

  const basicInfoItems = ref([
    {
      label: '职业类型',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.positionType
      },
    },
    {
      label: '职位分类',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.positionName
      },
    },
    {
      label: '职位名称',
      span: 2,
      type: 'value',
      value: (data) => {
        return data.extraData
      },
    },
    {
      label: '职位描述',
      span: 2,
      type: 'html',
      value: (data) => {
        return data?.positionDesc?.replace(/\n/g, '<br>')
      },
    },
    {
      label: '职位关键词',
      span: 2,
      type: 'tags',
      value: (data) => {
        return [
          ...new Set(
            data?.requiredKeywords
              ?.map((label) => label)
              .concat(data?.customKeywords?.map((label) => label)),
          ),
        ]
      },
    },
    {
      label: '经验',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.experience
      },
    },
    {
      label: '学历',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.qualification
      },
    },
    {
      label: '结算方式',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '兼职招聘'
      },
      value: (data) => {
        return data.paymentMethod
      },
    },
    {
      label: '薪资范围',
      span: 1,
      type: 'value',
      value: (data) => {
        return `${data.minSalary}${data.unit}-${data.maxSalary}${data.unit}*${data.salaryStandards}`
      },
    },
    {
      label: '底薪',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '社招全职' && isSales.value
      },
      value: (data) => {
        return data.basicSalary
      },
    },
    {
      label: '工作地点',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.addressInfo
      },
    },
    {
      label: '毕业时间',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '应届校园招聘'
      },
      value: (data) => {
        return `${data.minGradeTime}-${data.maxGradeTime}`
      },
    },
    {
      label: '实习要求',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '实习生招聘'
      },
      value: (data) => {
        return `最少实习月数：${data.minInternshipMonths} - 最少周到岗天数：${data.minWeekNums}`
      },
    },
    {
      label: '兼职时间',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '兼职招聘'
      },
      value: (data) => {
        const workDateLabel = data.workDate.includes('|')
          ? '自定义' + data.workDate.split('|')[1].split(',').join(' - ')
          : data.workDate

        console.log(data.workTime.includes('|'), data.workTime)
        const workTimeLabel = data.workTime.includes('|')
          ? data.workTime.split('|')[0] +
            '：' +
            data.workTime
              .split('|')[1]
              .split(',')
              .map((item) => item.split('-').join(' - '))
              .join('，')
          : data.workTime

        return `工作日期：${workDateLabel}，工作时间段：${data.workTimePeriod}，每周工作天数：${data.workDaysWeek}，工作时间：${workTimeLabel}`
      },
    },
    {
      label: '招聘截止时间',
      span: 1,
      type: 'value',
      visible: (data) => {
        return data.positionType === '应届校园招聘' || data.positionType === '兼职招聘'
      },
      value: (data) => {
        return data.recruitDeadline
      },
    },
    {
      label: '所属部门',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.deptName
      },
    },
  ])
  const scoreWeightItems = ref([
    {
      label: '教育背景',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.eduWeight
      },
    },
    {
      label: '工作能力',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.workingAbilityWeight
      },
    },
    {
      label: '语言能力',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.languageAbilityWeight
      },
    },
    {
      label: '荣誉奖项',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.awardWeight
      },
    },
    {
      label: '技能证书',
      span: 1,
      type: 'value',
      value: (data) => {
        return data.skillsWeight
      },
    },
  ])

  const positionNameOptions = ref([])
  const isSales = computed(() => {
    return positionNameOptions.value.some(
      (item) => item.positionName === detailData.value.positionName && item.basicSalary,
    )
  })

  function getPositionNameOptions(query) {
    if (!query) {
      positionNameOptions.value = []
      return
    }
    getJobNameOptions({
      positionType: detailData.value.positionType,
      positionName: query,
    }).then((data) => {
      positionNameOptions.value = data
    })
  }

  watchEffect(() => {
    if (detailData.value.positionName) {
      getPositionNameOptions(detailData.value.positionName)
    }
  })

  // 职位状态
  const jobStatus = computed(() => {
    return detailData.value.status || ''
  })

  defineExpose({
    refresh() {
      getDetailData()
    },
  })
</script>

<style lang="scss" scoped>
  .job-detail {
    position: relative;

    &_watermark {
      display: flex;
      position: absolute;
      top: 10px;
      right: 0px;
      align-items: center;
      justify-content: center;
      width: 160px;
      height: 160px;

      & > i {
        font-size: 160px;
      }
    }
  }
</style>
