<!-- eslint-disable vue/no-v-html -->
<template>
  <ElDescriptions
    class="description_items"
    :border="true"
    :column="2"
    :title="title"
    :label-width="146">
    <ElDescriptionsItem
      v-for="(item, index) in visibleItems"
      :key="index"
      :label="item.label"
      :span="item.span"
      label-width="146px">
      <template v-if="item.type === 'value'">
        {{ getValue(item) }}
      </template>
      <template v-else-if="item.type === 'html'">
        <div v-html="getValue(item)" />
      </template>
      <template v-else-if="item.type === 'tags'">
        <div class="flex flex-wrap gap-[4px]">
          <ElTag
            v-for="tag in getValue(item)"
            :key="tag"
            type="primary">
            {{ tag }}
          </ElTag>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
  const { items, title, data } = defineProps({
    title: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  })

  const visibleItems = computed(() => {
    return items.filter((item) => {
      if (item.visible) {
        return item.visible(data)
      } else {
        return true
      }
    })
  })

  function getValue(item) {
    if (typeof item.value === 'function') {
      return item.value(data)
    } else {
      return item.value
    }
  }
</script>

<style lang="scss" scoped>
  .description_items {
    :deep(table) {
      table-layout: fixed;
    }
  }
</style>
