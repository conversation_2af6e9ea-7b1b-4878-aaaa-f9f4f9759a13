<template>
  <div class="detail">
    <PageHeader content="查看职位">
      <template #extra>
        <!-- 已发布时显示关闭按钮 -->
        <ElButton
          v-if="jobStatus === '已发布'"
          type="primary"
          :loading="closeLoading"
          @click="handleClose">
          关闭职位
        </ElButton>
        <!-- 已关闭时显示删除、编辑、发布按钮 -->
        <template v-if="jobStatus === '已关闭'">
          <!-- 没有关联简历时才显示删除\编辑按钮  -->
          <template v-if="!hasResume">
            <ElButton
              type="danger"
              :plain="true"
              :loading="deleteLoading"
              @click="handleDelete">
              删除
            </ElButton>
            <ElButton
              type="primary"
              :plain="true"
              @click="handleEdit">
              编辑
            </ElButton>
          </template>
          <ElButton
            type="primary"
            :loading="publishLoading"
            @click="handlePublish">
            发布职位
          </ElButton>
        </template>
        <!-- 发布失败时显示删除、编辑按钮 -->
        <template v-if="jobStatus === '发布失败'">
          <ElButton
            v-if="!hasResume"
            type="danger"
            :plain="true"
            :loading="deleteLoading"
            @click="handleDelete">
            删除
          </ElButton>
          <ElButton
            type="primary"
            :plain="true"
            @click="handleEdit">
            编辑
          </ElButton>
        </template>
        <!-- 草稿时显示删除、编辑、发布按钮 -->
        <template v-if="jobStatus === '草稿'">
          <ElButton
            v-if="!hasResume"
            type="danger"
            :plain="true"
            :loading="deleteLoading"
            @click="handleDelete">
            删除
          </ElButton>
          <ElButton
            type="primary"
            :plain="true"
            @click="handleEdit">
            编辑
          </ElButton>
          <ElButton
            type="primary"
            :loading="publishLoading"
            @click="handlePublish">
            发布职位
          </ElButton>
        </template>
      </template>
    </PageHeader>
    <div class="detail_content">
      <JobDetail
        ref="jobDetailRef"
        :job-id="jobId"
        @data="handleData" />
    </div>
  </div>
</template>

<script setup>
  import { closeJob, deleteJob, publishJob } from '@/apis/job'
  import { PageHeader } from '@/components'
  import { checkLogin } from '../utils'
  import JobDetail from './components/job-detail.vue'

  const route = useRoute()
  const router = useRouter()

  // 职位 ID
  const jobId = computed(() => {
    return route.query.jobId || ''
  })

  // 职位状态
  const jobStatus = computed(() => {
    return detailData.value.status || ''
  })

  // 是否有关联简历
  const hasResume = computed(() => {
    return detailData.value.resumeCount > 0
  })

  // 详情数据
  const detailData = ref({})
  function handleData(data) {
    detailData.value = data
  }

  // 关闭
  const closeLoading = ref(false)
  const jobDetailRef = useTemplateRef()
  async function handleClose() {
    closeLoading.value = true
    try {
      const isLogin = await checkLogin()
      if (!isLogin) return
      await closeJob({ bid: jobId.value })
      ElMessage.success('职位已关闭')
      jobDetailRef.value.refresh()
    } catch (error) {
      console.log(error)
    } finally {
      closeLoading.value = false
    }
  }

  // 编辑
  function handleEdit() {
    router.push({
      path: '/jobs/edit',
      query: {
        jobId: jobId.value,
      },
    })
  }

  // 发布
  const publishLoading = ref(false)
  async function handlePublish() {
    publishLoading.value = true
    try {
      const isLogin = await checkLogin()
      if (!isLogin) return
      await publishJob({ bid: jobId.value })
      ElMessage.success('职位已发布')
      jobDetailRef.value.refresh()
    } catch (error) {
      console.log(error)
    } finally {
      publishLoading.value = false
    }
  }

  // 删除
  const deleteLoading = ref(false)
  function handleDelete() {
    deleteLoading.value = true
    deleteJob({ bid: jobId.value })
      .then(() => {
        ElMessage.success('职位已删除')
        router.push('/jobs')
      })
      .finally(() => {
        deleteLoading.value = false
      })
  }
</script>

<style lang="scss" scoped>
  .detail {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
    }
  }
</style>
