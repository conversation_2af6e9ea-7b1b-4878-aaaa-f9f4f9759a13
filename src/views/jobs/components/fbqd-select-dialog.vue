<template>
  <ElDialog
    v-model="model"
    width="480"
    title="发布渠道选择">
    <ElFormItem label="发布渠道">
      <ElSelect
        v-model="chose"
        placeholder="请选择发布渠道"
        :clearable="true">
        <ElOption
          v-for="item in JOB_FBQD"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </ElSelect>
    </ElFormItem>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="model = false">取消</ElButton>
        <ElButton
          :loading="loading"
          type="primary"
          @click="emits('confirm', chose)">
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
  import { useDict } from '@/stores/dict'
  const emits = defineEmits('confirm')
  const { loading } = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
  })
  const model = defineModel({
    type: Boolean,
    default: false,
  })
  const chose = ref('后台管理')
  const { JOB_FBQD } = useDict('JOB_FBQD')
</script>

<style lang="scss" scoped></style>
