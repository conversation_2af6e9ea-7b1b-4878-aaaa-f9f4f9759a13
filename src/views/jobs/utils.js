import { checkBossLogin } from '@/apis/channel'

// 检查 BOSS 直聘是否为登录状态
export function checkLogin() {
  return new Promise((resolve, reject) => {
    checkBossLogin()
      .then((res) => {
        const isBossLogin = res === '已登录'

        if (!isBossLogin) {
          ElMessageBox({
            title: '登录提醒',
            message: h('p', null, [
              h('span', null, '系统监测到用户当前未在'),
              h('span', { class: 'text-[#409EFF]' }, '“渠道管理”'),
              h('span', null, '中登录 BOSS 直聘账号，是否现在前往登录？'),
            ]),
            type: 'warning',
            showCancelButton: true,
            confirmButtonText: '前往登录',
            cancelButtonText: '稍后登录',
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                window.open('/channel', '_self')
              } else {
                done()
              }
            },
          })
        }

        resolve(isBossLogin)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
