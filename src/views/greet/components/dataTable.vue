<template>
  <TableWrapper>
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="所属账号"
          prop="createdByName">
          <UserNameInput
            v-model="searchData.createdByName"
            style="width: 260px" />
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            :loading="loading"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="loading"
        :data="tableData"
        :max-height="maxHeight"
        :border="true">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无匹配数据" />
        </template>
        <ElTableColumn
          label="职位ID"
          prop="positionBid" />
        <ElTableColumn
          label="职位名称"
          prop="positionName" />
        <ElTableColumn
          label="职位类型"
          prop="positionType">
          <template #default="{ row }">
            {{ getDictLabel('JOB_TYPE', row.positionType) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="职位状态"
          prop="positionStatus">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  已发布: 'success',
                  已关闭: 'info',
                  发布中: 'warning',
                  发布失败: 'danger',
                  草稿: 'primary',
                }[row.positionStatus]
              ">
              {{ getDictLabel('JOB_STATUS', row.positionStatus) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          width="100"
          label="发布渠道"
          prop="releaseChannel">
          <template #default="{ row }">
            <!-- 发布过的职位显示 “Boss直聘” -->
            {{
              row.positionStatus === '已发布' || row.positionStatus === '已关闭'
                ? row.releaseChannel
                : '-'
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          width="100"
          label="匹配状态"
          prop="matchStatus">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  1: 'primary', // 匹配中
                  2: 'danger', // 待处理 从未打过招呼或者有失败的情况
                  3: 'warning', // 处理中
                  4: 'success', // 已完成
                  5: 'danger', // 失败
                }[row.matchStatus]
              ">
              {{ getDictLabel('GREET_MATCH_STATUS', row.matchStatus) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          width="100"
          label="打招呼人数"
          prop="greetCount" />
        <ElTableColumn
          width="100"
          label="所属账号"
          prop="createdByName" />
        <ElTableColumn
          label="匹配时间"
          prop="matchTime" />
        <ElTableColumn
          label="操作"
          width="100">
          <template #default="{ row }">
            <!-- 匹配中时禁用查看。 -->
            <ElButton
              :disabled="row.matchStatus === 1"
              :link="true"
              type="primary"
              @click="handleView(row)">
              查看
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper" />
    </template>
  </TableWrapper>
</template>

<script setup>
  import { getGreetMatchList } from '@/apis/greet'
  import { UserNameInput } from '@/components'
  import { getDictLabel, useDict } from '@/stores/dict'
  import { actionDelegationMiddleware, usePagination } from 'alova/client'
  import TableWrapper from './tableWrapper.vue'
  useDict('JOB_TYPE', 'JOB_STATUS', 'GREET_MATCH_STATUS')

  const props = defineProps({
    jobId: {
      type: String,
      default: '',
    },
  })

  const searchFormRef = useTemplateRef('searchFormRef')
  const searchData = reactive({
    createdByName: '',
  })

  // 查询
  function handleSearch() {
    reload()
  }
  // 重置
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  // 表格数据
  const {
    // 加载状态
    loading,

    // 列表数据
    data: tableData,

    // 当前页码，改变此页码将自动触发请求
    page,

    // 每页数据条数
    pageSize,

    // 总数据量
    total,

    reload,
  } = usePagination(
    // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
    (page, pageSize) =>
      getGreetMatchList({
        pageNum: page,
        pageSize,
        positionBid: props.jobId,
        createdByName: searchData.createdByName,
      }),
    {
      // 请求前的初始数据（接口返回的数据格式）
      initialData: {
        total: 0,
        list: [],
      },
      total: (response) => response.total,
      data: (response) => response.list,
      initialPage: 1, // 初始页码，默认为1
      initialPageSize: 50, // 初始每页数据条数，默认为10
      watchingStates: [toRef(props, 'jobId'), toRef(searchData, 'createdByName')],
      middleware: actionDelegationMiddleware('getGreetMatchList'),
      debounce: 1000,
    },
  )

  const router = useRouter()
  // 查看匹配详情
  function handleView(item) {
    router.push(`/greet/detail/${item.positionBid}/${item.bid}`)
  }
</script>

<style lang="scss" scoped></style>
