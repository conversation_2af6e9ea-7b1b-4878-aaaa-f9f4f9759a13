<template>
  <div class="table-wrapper">
    <slot name="search" />
    <div
      ref="tableRef"
      class="table_main">
      <slot
        name="table"
        :max-height="tableMaxHeight" />
    </div>
    <div class="footer">
      <slot name="pagination" />
    </div>
  </div>
</template>

<script setup>
  import { useElementSize } from '@vueuse/core'

  const { height: tableMaxHeight } = useElementSize(useTemplateRef('tableRef'))
</script>

<style lang="scss" scoped>
  .table-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .table_main {
      flex: 1;
      width: 100%;
      overflow: auto;
    }

    .footer {
      display: flex;
      flex: 0;
      justify-content: flex-end;
      padding: 15px 0;
    }
  }
</style>
