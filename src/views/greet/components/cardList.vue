<template>
  <TableWrapper>
    <template #table="{ maxHeight }">
      <ElScrollbar
        v-loading="loading"
        :height="maxHeight">
        <ElEmpty
          v-if="isEmpty(tableData) && !loading"
          :image-size="160"
          description="暂无打招呼数据" />
        <div
          v-else
          class="card_grid">
          <PersonCard
            v-for="item in tableData"
            :key="item.bid"
            :person="item"
            @handle-view="handleView" />
        </div>
      </ElScrollbar>
      <!-- 候选人侧边详情 -->
      <CandidateInfoDrawer
        v-model:visible="drawerVisible"
        :candidate-info="curCandidateInfo"
        @refresh="refresh" />
    </template>
    <template #pagination>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper" />
    </template>
  </TableWrapper>
</template>

<script setup>
  import { getGreetList } from '@/apis/greet'
  import { isEmpty } from '@/utils/is'
  import { usePagination } from 'alova/client'
  import CandidateInfoDrawer from '../detail/components/candidateInfoDrawer.vue'
  import PersonCard from './personCard.vue'
  import TableWrapper from './tableWrapper.vue'

  const props = defineProps({
    jobId: {
      type: String,
      default: '',
    },
  })

  // 表格数据
  const {
    // 加载状态
    loading,

    // 列表数据
    data: tableData,

    // 当前页码，改变此页码将自动触发请求
    page,

    // 每页数据条数
    pageSize,

    // 总数据量
    total,

    refresh,
  } = usePagination(
    // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
    (page, pageSize) =>
      getGreetList({
        pageNum: page,
        pageSize,
        positionBid: props.jobId,
      }),
    {
      // 请求前的初始数据（接口返回的数据格式）
      initialData: {
        total: 0,
        list: [],
      },
      total: (response) => response.total,
      data: (response) => response.list,
      initialPage: 1, // 初始页码，默认为1
      initialPageSize: 50, // 初始每页数据条数，默认为10
      watchingStates: [toRef(props, 'jobId')],
    },
  )

  // 当前所点击的
  const curCandidateInfo = ref({})
  // 详情弹窗
  const drawerVisible = ref(false)
  // 查看匹配详情
  function handleView(row) {
    try {
      // 简历信息
      const resumeDetails = JSON.parse(JSON.parse(row.details))
      curCandidateInfo.value = {
        resumeDetails,
        ...row,
      }
    } catch (error) {
      console.error('解析候选人简历详情失败', error)
      curCandidateInfo.value = {
        ...row,
      }
    }
    // 打开弹窗
    drawerVisible.value = true
  }
</script>

<style lang="scss" scoped>
  .card_grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(580px, 1fr)); /* 关键属性 */
    gap: 16px;
  }
</style>
