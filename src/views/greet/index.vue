<template>
  <TablePageLayout title="打招呼管理">
    <template #header-extra>
      <ElSelect
        v-model="selectedJobId"
        placeholder="全部职位"
        :clearable="true"
        style="width: 280px">
        <ElOption
          v-for="item in jobsOptions"
          :key="item.positionBid"
          :filterable="true"
          :label="item.positionName + `(${item.positionBid})`"
          :value="item.positionBid" />
      </ElSelect>
    </template>
    <template #search>
      <div class="top_bar">
        <ElSpace
          :size="16"
          :spacer="
            h('span', {
              class: 'text-[#DEDFE0] text-[18px]',
              innerHTML: '/',
            })
          ">
          <div class="flex items-center">
            <i class="iconfont icon-match mr-[10px] text-[20px]!"></i>
            <span class="mr-[3px] text-[12px]/[20px] text-[#606266]">已匹配：</span>
            <p class="text-[20px]/[28px]">
              <span class="text-[#0055FF]">{{ countData.matchCount || 0 }}</span>
              <span class="text-[14px]">人</span>
            </p>
          </div>
          <div class="flex items-center">
            <i class="iconfont icon-match mr-[10px] text-[20px]!"></i>
            <span class="mr-[3px] text-[12px]/[20px] text-[#606266]">打招呼：</span>
            <p class="text-[20px]/[28px]">
              <span class="text-[#0055FF]">{{ countData.greetCount || 0 }}</span>
              <span class="text-[14px]">人</span>
            </p>
          </div>
        </ElSpace>
        <div class="right">
          <AiButton
            :loading="matchBtnLoading || canMatchBtnLoading"
            :disabled="!canAiMatch"
            @click="onAiMatch">
            AI寻人
          </AiButton>
        </div>
      </div>
      <ElButtonGroup class="mt-[16px] mb-[20px]">
        <ElButton
          :type="activeTab === 1 ? 'primary' : ''"
          @click="onChangeTab(1)">
          匹配列表
        </ElButton>
        <ElButton
          :type="activeTab === 2 ? 'primary' : ''"
          @click="onChangeTab(2)">
          打招呼列表
        </ElButton>
      </ElButtonGroup>
    </template>
    <template #table>
      <DataTable
        v-show="activeTab === 1"
        :job-id="selectedJobId" />
      <CardList
        v-show="activeTab === 2"
        :job-id="selectedJobId" />

      <!-- 选择职位 -->
      <PublishedJobDialog
        v-model:visible="jobsDialogVisible"
        :api="getJobListInGreet"
        @confirm="onAiMatchConfirm">
        <template #alert>
          <div class="alert_info">
            <i class="icon-info-filled iconfont"></i>
            选择职位后AI将自动匹配候选人，每次匹配将反馈10位，再由您决定打招呼的人选。
          </div>
        </template>
      </PublishedJobDialog>
    </template>
  </TablePageLayout>
</template>

<script setup>
  import { addMatchGreet, getCanAiMatch, getGreetCount, getGreetPositionList } from '@/apis/greet'
  import { getJobListInGreet } from '@/apis/job'
  import { AiButton, PublishedJobDialog, TablePageLayout } from '@/components'
  import { accessAction, useRequest, useWatcher } from 'alova/client'
  import { h } from 'vue'
  import { checkLogin } from '../jobs/utils'
  import CardList from './components/cardList.vue'
  import DataTable from './components/dataTable.vue'

  // 当前激活tab
  const activeTab = ref(1)
  function onChangeTab(tab) {
    activeTab.value = tab
  }

  /**
   * 判断是否可以AI寻人
   * + 当前账号无待处理和处理中
   */
  const {
    loading: canMatchBtnLoading,
    data: canAiMatch,
    send: getCanAiMatchApi,
  } = useRequest(getCanAiMatch, {
    initialData: false,
  })

  // 当前选中的职位bif
  const selectedJobId = ref('')
  // 获取匹配过的职位列表 进行下拉
  const { data: jobsOptions } = useRequest(getGreetPositionList, {
    initialData: [],
  })

  // 获取统计信息
  const { data: countData } = useWatcher(
    () =>
      getGreetCount({
        positionBid: selectedJobId.value,
      }),
    [selectedJobId],
    {
      initialData: {},
      immediate: true,
    },
  )

  const jobsDialogVisible = ref(false)
  /**
   * AI寻人
   * + 先判断是否登录
   * - 匹配列表区记录为0时，功能区【AI寻人】按钮亮起，点击跳出选择职位弹窗，可进行AI寻人。
   * - 匹配列表区存在“匹配中、待处理、处理中”的任一记录时，【AI寻人】按钮为灰，不可点击，不支持再次AI寻人。
   */
  async function onAiMatch() {
    let loading = null
    try {
      loading = ElLoading.service({
        lock: true,
      })
      const isLogin = await checkLogin()
      if (!isLogin) {
        throw new Error('请先登录')
      }
      jobsDialogVisible.value = true
      loading.close()
    } catch (error) {
      console.error('AI寻人选择职位失败', error)
      loading.close()
    }
  }

  const { loading: matchBtnLoading, send: sendMatchApi } = useRequest(addMatchGreet, {
    immediate: false,
  })
  async function onAiMatchConfirm(jobItem) {
    let loading = null
    try {
      loading = ElLoading.service({
        lock: true,
      })
      await sendMatchApi({
        positionBid: jobItem.bid,
        positionName: jobItem.positionName,
        positionType: jobItem.positionType,
        positionStatus: jobItem.status,
        releaseChannel: jobItem.releaseChannel,
        bossId: jobItem.bossId,
        isCharge: jobItem.isCharge,
      })
      // 触发列表刷新
      accessAction('getGreetMatchList', (delegatedActions) => {
        delegatedActions.reload()
      })
      // 重新触发是否可以AI寻人状态
      getCanAiMatchApi()
      loading.close()
      jobsDialogVisible.value = false
    } catch (error) {
      loading.close()
      console.log('AI寻人确认职位失败', error)
    }
  }
</script>

<style lang="scss" scoped>
  .top_bar {
    display: flex;
    justify-content: space-between;
    height: 48px;
    padding: 10px;
    border-radius: 4px 4px 4px 4px;
    background: #fafafa;
  }

  .alert_info {
    width: 100%;
    height: 40px;
    margin-bottom: 16px;
    border-radius: 4px 4px 4px 4px;
    background: #ecf5ff;
    color: #409eff;
    font-size: 14px;
    line-height: 3em;
    text-align: center;
  }
</style>
