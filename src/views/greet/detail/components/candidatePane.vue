<template>
  <ElScrollbar>
    <div class="pane">
      <ElSpace class="mb-[20px]">
        <ElButton
          :icon="hiIcon"
          type="primary"
          :disabled="greetDisabled"
          @click="handleGreetSelected">
          一键打招呼
        </ElButton>
        <ElButton
          :plain="true"
          :icon="delIcon"
          type="danger"
          :disabled="eliminateDisabled"
          @click="handleEliminateAll">
          淘汰全部
        </ElButton>
      </ElSpace>
      <div
        ref="tableRef"
        class="table_main">
        <ElTable
          ref="tableInstanceRef"
          v-loading="loading"
          :data="tableData"
          :max-height="tableMaxHeight"
          :border="true"
          @selection-change="handleSelectionChange">
          <template #empty>
            <ElEmpty
              :image-size="160"
              description="暂候选人列表数据" />
          </template>
          <ElTableColumn
            type="selection"
            :selectable="selectable"
            width="50" />
          <ElTableColumn
            type="index"
            width="50" />
          <ElTableColumn
            label="候选人ID"
            prop="bid" />
          <ElTableColumn
            width="100"
            label="候选人姓名"
            prop="name" />
          <ElTableColumn
            width="100"
            label="年龄"
            prop="age" />
          <ElTableColumn
            label="经验"
            prop="wordYear"
            :show-overflow-tooltip="true" />
          <ElTableColumn
            width="150"
            label="最高学历"
            prop="highestEducation"
            :show-overflow-tooltip="true" />
          <ElTableColumn
            label="最近工作经历"
            prop="workExperience"
            :show-overflow-tooltip="true" />
          <ElTableColumn
            width="100"
            label="匹配度"
            prop="matchPercentage" />
          <!-- 打过招呼才显示 -->
          <template v-if="isChangeStatus">
            <ElTableColumn
              width="100"
              label="打招呼">
              <template #default="{ row }">
                <ElTag
                  v-if="!isEmpty(row.greetStatus)"
                  :type="
                    {
                      1: 'success', // 成功
                      2: 'danger', // 失败
                      3: 'info', // 淘汰
                      4: 'primary', // 处理中
                    }[row.greetStatus]
                  ">
                  {{ getDictLabel('GREET_CANDIDATE_STATUS', row.greetStatus) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn
              v-if="showViewBtnCol"
              label="候选人详情"
              width="100">
              <template #default="{ row }">
                <ElButton
                  v-if="row.greetStatus == 1"
                  :link="true"
                  type="primary"
                  @click="handleView(row)">
                  查看
                </ElButton>
                <span v-else>- -</span>
              </template>
            </ElTableColumn>
          </template>
        </ElTable>
      </div>
    </div>
    <!-- 候选人详情 -->
    <CandidateInfoDrawer
      v-model:visible="drawerVisible"
      :candidate-info="curCandidateInfo"
      @refresh="refresh" />
  </ElScrollbar>
</template>

<script setup>
  import { disuseCandidate, getCandidateList, greetCandidate } from '@/apis/greet'
  import { getDictLabel, useDict } from '@/stores/dict'
  import { isEmpty } from '@/utils/is'
  import { checkLogin } from '@/views/jobs/utils'
  import { useElementSize } from '@vueuse/core'
  import { usePagination, useRequest } from 'alova/client'
  import CandidateInfoDrawer from './candidateInfoDrawer.vue'
  // 候选人打招呼状态
  useDict('GREET_CANDIDATE_STATUS')

  const hiIcon = h('i', { class: 'iconfont icon-sayhello' })
  const delIcon = h('i', { class: 'iconfont icon-eliminate' })
  const { height: tableMaxHeight } = useElementSize(useTemplateRef('tableRef'))

  const route = useRoute()
  // 匹配任务bid
  const matchBId = route.params.bid

  // 表格实例引用
  const tableInstanceRef = ref()
  // 选中的候选人列表
  const selectedCandidates = ref([])

  // 表格数据
  const {
    // 加载状态
    loading,

    // 列表数据
    data: tableData,

    // 刷新当前页
    refresh,
  } = usePagination(
    // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
    (page, pageSize) =>
      getCandidateList({
        pageNum: page,
        pageSize,
        matchRecordBid: matchBId,
      }),
    {
      // 请求前的初始数据（接口返回的数据格式）
      initialData: {
        total: 0,
        list: [],
      },
      total: (response) => response.total,
      data: (response) => response.list,
      initialPage: 1, // 初始页码，默认为1
      initialPageSize: 50, // 初始每页数据条数，默认为10
    },
  )

  // 可以规定禁用项
  // 类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选
  const selectable = (row) => {
    // 不存在打招呼状态 或者 状态为失败
    return isEmpty(row.greetStatus) ? true : row.greetStatus == 2
  }

  /**
   * 是否操作过
   * + 只要有一个打招呼状态就说明操作过
   */
  const isChangeStatus = computed(() => {
    return tableData.value.some((item) => !isEmpty(item.greetStatus))
  })
  /**
   * 是否显示操作按钮
   * + 只要有一个打招呼状态是成功的就要展示查看按钮
   */
  const showViewBtnCol = computed(() => {
    return tableData.value.some((item) => item.greetStatus == 1)
  })

  // 处理选择变化
  function handleSelectionChange(selection) {
    selectedCandidates.value = selection
  }

  // 打招呼按钮禁用：表格中每个人都打招呼成功或者淘汰
  const greetDisabled = computed(() => {
    return tableData.value.every(
      (item) => !isEmpty(item.greetStatus) && (item.greetStatus == '1' || item.greetStatus == '3'),
    )
  })

  // 打招呼
  const { send: sendGreetApi } = useRequest(greetCandidate, {
    immediate: false,
  })
  // 一键打招呼
  function handleGreetSelected() {
    if (selectedCandidates.value.length === 0) {
      ElMessage.warning('请先选择候选人')
      return
    }

    ElMessageBox({
      type: 'warning',
      title: '确认打招呼',
      message:
        '每次匹配您只有一次选择机会，未被选择的候选人将直接淘汰无法重新打招呼。是否确认只对选中的候选人打招呼？',
      showCancelButton: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '处理中...'

          try {
            // 检查是否登录BOSS
            const isLogin = await checkLogin()
            if (!isLogin) {
              throw new Error('请先登录')
            }
            await sendGreetApi({
              peopleDTOList: selectedCandidates.value.map((item) => ({
                bid: item.bid,
                securityId: item.securityId,
                encryptMarkId: item.encryptMarkId,
                lid: item.lid,
                markType: item.markType,
              })),
            })
            // 刷新当前页数据
            refresh()
            // 关闭
            done()
            instance.confirmButtonLoading = false
          } catch (error) {
            console.error('打招呼失败', error)
          }
        } else {
          done()
        }
      },
    }).catch(() => {
      // 用户取消操作
    })
  }

  // 淘汰全部禁用：表格中每个人都打招呼成功或者淘汰
  // 除去禁用的人中可操作的人只勾选了部分，也禁用淘汰全部按钮
  const eliminateDisabled = computed(() => {
    return (
      tableData.value.every(
        (item) =>
          !isEmpty(item.greetStatus) && (item.greetStatus == '1' || item.greetStatus == '3'),
      ) ||
      (selectedCandidates.value.length <
        tableData.value.filter((item) => selectable(item)).length &&
        selectedCandidates.value.length > 0)
    )
  })
  const { send: sendDisuseApi } = useRequest(disuseCandidate, {
    immediate: false,
  })
  // 淘汰全部
  function handleEliminateAll() {
    let message =
      // '每次匹配您只有一次选择机会，淘汰的候选人无法重新打招呼。是否确认淘汰选中的候选人？'
      h('p', null, [
        '每次匹配您只有一次选择机会，淘汰的候选人无法重新打招呼。是否确认淘汰',
        h('span', { class: 'text-[#f56c6c]' }, '选中的'),
        '候选人？',
      ])
    // 选中的人
    let bids = selectedCandidates.value.map((item) => item.bid)
    // 如果没有选择需要淘汰的人 文案为淘汰全部
    if (selectedCandidates.value.length === 0) {
      // message = '每次匹配您只有一次选择机会，淘汰的候选人无法重新打招呼。是否确认淘汰全部候选人？'
      message = h('p', null, [
        '每次匹配您只有一次选择机会，淘汰的候选人无法重新打招呼。是否确认淘汰',
        h('span', { class: 'text-[#f56c6c]' }, '全部'),
        '候选人？',
      ])
      // 为空淘汰所有失败或者没有打招呼状态的人
      bids = tableData.value
        .filter((item) => isEmpty(item.greetStatus) || item.greetStatus == 2)
        .map((item) => item.bid)
    }

    // 如果没有bid 则退出
    if (isEmpty(bids)) {
      return
    }

    ElMessageBox({
      title: '确认淘汰',
      type: 'warning',
      message: message,
      showCancelButton: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '处理中...'

          try {
            // 检查是否登录BOSS
            const isLogin = await checkLogin()
            if (!isLogin) {
              throw new Error('请先登录')
            }
            await sendDisuseApi({
              bids,
            })
            // 刷新当前页数据
            refresh()
            // 关闭
            done()
            instance.confirmButtonLoading = false
          } catch (error) {
            console.error('淘汰候选人失败', error)
          }
        } else {
          done()
        }
      },
    }).catch(() => {
      // 用户取消操作
    })
  }

  // 当前所点击的
  const curCandidateInfo = ref({})
  // 详情弹窗
  const drawerVisible = ref(false)
  // 查看匹配详情
  function handleView(row) {
    try {
      // 简历信息
      const resumeDetails = JSON.parse(JSON.parse(row.details))
      curCandidateInfo.value = {
        resumeDetails,
        ...row,
      }
    } catch (error) {
      console.error('解析候选人简历详情失败', error)
      curCandidateInfo.value = {
        ...row,
      }
    }
    // 打开弹窗
    drawerVisible.value = true
  }
</script>

<style lang="scss" scoped>
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  .pane {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .table_main {
      flex: 1;
      overflow: auto;
    }
  }
</style>
