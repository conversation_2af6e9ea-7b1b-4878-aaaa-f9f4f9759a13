<template>
  <ElDrawer
    v-model="visible"
    title="候选人详情"
    :size="1000"
    header-class="mb-[0px]!"
    @close="onClose"
    @open="oneOpen">
    <ElRow class="main">
      <ElCol
        :span="17"
        class="left">
        <ElScrollbar v-if="!isEmpty(candidateInfo.resumeDetails)">
          <div class="pr-[20px] pl-[12px]">
            <Resume :info="candidateInfo.resumeDetails" />
          </div>
        </ElScrollbar>
      </ElCol>
      <ElCol
        :span="7"
        class="right">
        <h1 class="mb-[16px] text-[18px]/[26px] font-bold">打招呼情况</h1>
        <ElSpace
          class="w-full"
          direction="vertical"
          :size="12"
          :fill="true">
          <ElRow
            :gutter="8"
            align="middle">
            <ElCol
              :span="9"
              class="text-[14px]/[22px] text-[#606266]">
              匹配值
            </ElCol>
            <ElCol
              :span="15"
              class="text-[20px]/[28px] text-[#0055FF]">
              {{ candidateInfo.matchPercentage }}
            </ElCol>
          </ElRow>
          <ElRow
            :gutter="8"
            align="middle">
            <ElCol
              :span="9"
              class="text-[14px]/[22px] text-[#606266]">
              打招呼时间
            </ElCol>
            <ElCol
              :span="15"
              class="text-[14px]/[22px] text-[#606266]">
              {{ candidateInfo.greetTime }}
            </ElCol>
          </ElRow>
          <ElRow
            :gutter="8"
            align="middle">
            <ElCol
              :span="9"
              class="text-[14px]/[22px] text-[#606266]">
              简历获取时间
            </ElCol>
            <ElCol
              :span="15"
              class="text-[14px]/[22px] text-[#606266]">
              {{ candidateInfo.hadResumeTime || '--' }}
            </ElCol>
          </ElRow>
          <ElRow
            :gutter="8"
            align="middle">
            <ElButton
              type="primary"
              :plain="true"
              :disabled="!candidateInfo.isBind"
              @click="handleViewTalent">
              <template #icon>
                <i class="iconfont icon-search1" />
              </template>
              查看人才
            </ElButton>
            <ElButton
              v-if="!candidateInfo.isBind"
              type="success"
              :plain="true"
              @click="handleBind">
              <template #icon>
                <i class="iconfont icon-link" />
              </template>
              绑定人才
            </ElButton>
            <ElButton
              v-else
              type="danger"
              :plain="true"
              @click="handleUnbind">
              <template #icon>
                <i class="iconfont icon-eliminate" />
              </template>
              解除绑定
            </ElButton>
          </ElRow>
        </ElSpace>
        <ResumeStatus
          :active="!!candidateInfo.isBind"
          class="resume_status" />
      </ElCol>
    </ElRow>
  </ElDrawer>
  <ElDialog
    v-model="bindDialogVisible"
    title="绑定人才"
    width="480px"
    @close="handleBindCancel">
    <ElAlert
      title="注：输入人才ID进行绑定，该人才的最新简历将与候选人绑定。"
      type="primary"
      :show-icon="true"
      :closable="false" />
    <ElForm
      ref="bindFormRef"
      label-width="70px"
      class="mt-[12px]"
      :model="bindFormData"
      :rules="bindFormRules">
      <ElFormItem
        label="人才ID"
        prop="input">
        <ElInput
          v-model="bindFormData.input"
          placeholder="请输入人才ID" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleBindCancel">取消</ElButton>
      <ElButton
        type="primary"
        :loading="bindLoading"
        @click="handleBindConfirm">
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { bindTalent, unbindTalent } from '@/apis/greet'
  import { Resume } from '@/components'
  import { isEmpty } from '@/utils/is'

  const { candidateInfo } = defineProps({
    // 当前候选人简历信息
    candidateInfo: {
      type: Object,
      default: () => ({}),
    },
  })

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  function oneOpen() {
    console.log('open')
  }

  // 关闭dialog时触发事件
  function onClose() {
    console.log('close')
  }

  // // 关闭dialog
  // function closeDialog() {
  //   visible.value = false
  // }

  // 查看人才
  const router = useRouter()
  function handleViewTalent() {
    router.push({
      path: '/resume/talents/detail',
      query: {
        id: candidateInfo.talentBid,
      },
    })
  }

  const emit = defineEmits(['refresh'])

  // 绑定人才
  const bindDialogVisible = ref(false)
  const bindFormData = reactive({
    input: '',
  })
  const bindFormRules = reactive({
    input: [{ required: true, message: '请输入人才ID', trigger: 'blur' }],
  })
  function handleBind() {
    bindDialogVisible.value = true
  }

  function handleBindCancel() {
    bindDialogVisible.value = false
    bindFormRef.value.resetFields()
  }

  const bindFormRef = useTemplateRef('bindFormRef')
  const bindLoading = ref(false)
  function handleBindConfirm() {
    bindFormRef.value.validate((valid) => {
      if (valid) {
        bindLoading.value = true
        bindTalent({
          bid: candidateInfo.bid,
          input: bindFormData.input,
        })
          .then(() => {
            ElMessage.success('绑定成功')
            handleBindCancel()

            visible.value = false
            emit('refresh')
          })
          .finally(() => {
            bindLoading.value = false
          })
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }

  // 解除绑定
  function handleUnbind() {
    ElMessageBox.confirm('确认解除绑定吗？解除后候选人简历将会变为未获取状态', '提示', {
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '处理中...'

          try {
            await unbindTalent(candidateInfo.bid)
            ElMessage.success('解除绑定成功')
            done()

            visible.value = false
            emit('refresh')
          } catch (error) {
            console.error('解除绑定失败', error)
          } finally {
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      },
    })
  }
</script>

<style lang="scss" scoped>
  .main {
    height: 100%;
    overflow: hidden;
    .left {
      height: 100%;
      // padding: 0 20px 0 12px;
      overflow: auto;
      border-right: 1px solid #dcdfe6;
    }

    .right {
      position: relative;
      padding: 20px 0 0 20px;

      .resume_status {
        position: absolute;
        top: -30px;
        right: 0px;
        width: 100px;
      }
    }
  }
</style>
