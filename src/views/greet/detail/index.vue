<template>
  <div class="detail">
    <PageHeader content="匹配详情"></PageHeader>
    <div class="detail_content">
      <ElTabs
        v-model="activeTab"
        type="border-card">
        <ElTabPane
          name="candidate"
          label="候选人列表">
          <CandidatePane />
        </ElTabPane>
        <ElTabPane
          name="job"
          label="职位信息">
          <ElScrollbar>
            <JobDetail :job-id="pid" />
          </ElScrollbar>
        </ElTabPane>
      </ElTabs>
    </div>
  </div>
</template>

<script setup>
  import { PageHeader } from '@/components'
  import JobDetail from '@/views/jobs/detail/components/job-detail.vue'
  import CandidatePane from './components/candidatePane.vue'

  const route = useRoute()
  // 职位id
  const pid = route.params.pid

  const activeTab = ref('candidate')
</script>

<style lang="scss" scoped>
  .detail {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
      :deep(.el-tabs) {
        width: 100%;
        height: 100%;

        .el-tabs__content {
          // height: calc(100% - 54px);
          // flex: 1 0 auto;

          .el-tab-pane {
            height: 100%;
            overflow: auto;
          }
        }
      }
    }
  }
</style>
