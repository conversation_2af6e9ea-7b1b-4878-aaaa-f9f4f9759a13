<template>
  <TablePageLayout title="企业信息管理">
    <template #header-extra>
      <ElInput
        v-model="searchData.name"
        style="width: 360px"
        placeholder="请输入关键词"
        :clearable="true"
        @clear="handleSearch">
        <template #append>
          <ElButton
            :icon="Search"
            @click="handleSearch" />
        </template>
      </ElInput>
    </template>
    <template #search>
      <div class="flex">
        <div class="flex-1">
          <FilePath
            :paths="paths"
            @change="handleDownload"></FilePath>
        </div>
        <div class="ml-[24px]">
          <CreateFolder
            :parent-folder-bid="paths[paths.length - 1].bid"
            @change="handleCreateFolder" />
          <ElDivider
            style="margin: 0 12px"
            direction="vertical" />
          <UploadBtn
            :folder-bid="paths[paths.length - 1].bid"
            @change="handleUpload" />
        </div>
      </div>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="tableLoading"
        style="margin-top: 18px"
        :max-height="maxHeight"
        :border="true"
        :data="currentTableData"
        @selection-change="handleSelectionChange">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无文件信息，点击右上方按钮添加文件夹/上传文件" />
        </template>
        <ElTableColumn
          type="selection"
          width="40" />
        <ElTableColumn
          label="文件/文件夹名称"
          prop="name">
          <template #default="{ row }">
            <ElLink
              v-if="row.type == 1"
              underline="never"
              :disabled="isTxtFile(row)"
              @click="handleDownload(row)">
              <i
                class="iconfont icon-document"
                style="margin-right: 9px" />
              {{ row.name }}
            </ElLink>
            <ElLink
              v-else-if="row.type == 0"
              underline="never"
              @click="handleDownload(row)">
              <i
                class="iconfont icon-qy_attachment-2"
                style="margin-right: 9px" />
              {{ row.name }}
            </ElLink>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="状态"
          prop="fastgptStatusCn">
          <template #default="{ row }">
            <ElTag
              v-if="row.type == 1 && row.fastgptStatusCn"
              :type="
                {
                  已就绪: 'success',
                  索引失败: 'danger',
                  索引中: 'warning',
                }[row.fastgptStatusCn]
              ">
              {{ getDictLabel('FILE_STATUS', row.fastgptStatusCn) }}
            </ElTag>
            <template v-else>-</template>
          </template>
        </ElTableColumn>
        <ElTableColumn label="创建/更新时间">
          <template #default="{ row }">
            {{ row.createdAt }}
            <br />
            {{ row.updatedAt }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作">
          <template #default="{ row }">
            <!-- 是文件且文件已就绪才可以查看文件，文件夹可以查看 -->
            <ElButton
              :disabled="(row.type == 1 && row.fastgptStatusCn !== '已就绪') || isTxtFile(row)"
              :link="true"
              type="primary"
              @click="handleDownload(row)">
              查看
            </ElButton>
            <!-- 是文件且文件正在索引中不可以删除，文件夹可以删除 -->
            <ElButton
              :disabled="row.type == 1 && row.fastgptStatusCn === '索引中'"
              :link="true"
              type="danger"
              @click="handleDelete(row)">
              删除
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #footer-extra>
      <div>
        <ElButton
          v-if="!showBatchBtns"
          :plain="true"
          type="primary"
          :text="true"
          @click="handleBatchOperate(true)">
          <template #icon>
            <i class="iconfont icon-delete" />
          </template>
          批量下载/删除
        </ElButton>
        <template v-else>
          <ElButton
            :link="true"
            :loading="batchDownloadLoading"
            @click="handleBatchDownload">
            <span class="text-[#409EFF]">下载</span>
            <template #icon>
              <i class="iconfont icon-download text-[#409EFF]" />
            </template>
          </ElButton>
          <ElButton
            :link="true"
            type="danger"
            :loading="batchDeleteLoading"
            @click="handleBatchDelete">
            删除
            <template #icon>
              <i class="iconfont icon-delete" />
            </template>
          </ElButton>
          <ElDivider direction="vertical" />
          <ElButton
            :link="true"
            @click="handleBatchOperate(false)">
            取消
          </ElButton>
        </template>
      </div>
    </template>
    <template #pagination>
      <!-- <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageNumChange" /> -->
    </template>
  </TablePageLayout>
</template>

<script setup>
  import {
    batchDelete,
    batchDownload,
    deleteFile,
    deleteFolder,
    getFileTree,
    getFileUrl,
    searchFile,
  } from '@/apis/enterprise'
  import { TablePageLayout } from '@/components'
  import { getDictLabel, useDict } from '@/stores/dict'
  import { downloadFileByBlob, downloadFileByLink } from '@/utils/download'
  import { Search } from '@element-plus/icons-vue'
  import CreateFolder from './components/create-folder.vue'
  import FilePath from './components/file-path.vue'
  import UploadBtn from './components/upload-btn.vue'

  useDict('FILE_STATUS')

  const searchData = reactive({
    name: '',
  })
  function handleSearch() {
    getTableData()
  }

  const paths = ref([
    {
      name: '根目录',
      bid: '',
      type: 0,
    },
  ])

  function handleCreateFolder(name) {
    console.log(name)
    getTableData()
  }
  function handleUpload(files) {
    console.log(files)
    getTableData()
  }

  onMounted(() => {
    getTableData()
  })

  const tableData = ref([])
  const tableLoading = ref(false)
  function getTableData() {
    tableLoading.value = true

    if (searchData.name) {
      searchFile({
        name: searchData.name,
      })
        .then((data) => {
          tableData.value = data
        })
        .finally(() => {
          tableLoading.value = false
        })
    } else {
      getFileTree()
        .then((data) => {
          tableData.value = data
          console.log(data)
        })
        .finally(() => {
          tableLoading.value = false
        })
    }
  }

  // 根据当前层级筛选出树形结构中展示的列表数据
  const currentPath = computed(() => {
    return paths.value[paths.value.length - 1]
  })
  const currentTableData = computed(() => {
    const find = (tree, path) => {
      // 根目录
      if (!path.bid) {
        return tree
      }

      // 遍历当前层级
      for (const node of tree) {
        if (node.bid === path.bid) {
          return node.children
        }
        if (node.children) {
          const result = find(node.children, path)
          if (result !== null) {
            return result
          }
        }
      }
      return null
    }

    const result = find(tableData.value, currentPath.value) || []
    console.log(result)
    const folders = result?.filter((item) => item.type == 0) || []
    const files = result?.filter((item) => item.type == 1) || []

    // 文件夹在上，文件在后排序
    return [...folders, ...files]
  })

  // 判断是txt文件
  function isTxtFile(row) {
    return row.type == 1 && row.name.endsWith('.txt')
  }
  // 下载
  function handleDownload(row) {
    // txt文件不支持下载
    if (isTxtFile(row)) {
      return
    }
    // 是文件：下载
    if (row.type == 1) {
      getFileUrl({
        fileBid: row.bid,
      }).then((data) => {
        downloadFileByLink(data, row.name, { target: '_blank' })
      })
    } else if (row.type == 0) {
      // 是文件夹：进入
      if (!row.bid) {
        paths.value = [
          {
            bid: '',
            name: '根目录',
            type: 0,
          },
        ]
        return
      }
      console.log(row)

      const index = paths.value.findIndex((item) => {
        return item.bid === row.bid
      })
      console.log(index)
      if (index !== -1) {
        paths.value = paths.value.slice(0, index + 1)
      } else {
        paths.value.push(row)
      }
    }
  }

  function handleDelete(row) {
    if (row.type == 1) {
      ElMessageBox.confirm('确认删除该文件及其所有数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            deleteFile(row.bid)
              .then(() => {
                ElMessage({
                  type: 'success',
                  message: '删除成功!',
                })
              })
              .finally(() => {
                done()
                getTableData()
                instance.confirmButtonLoading = false
              })
          } else {
            done()
            ElMessage({
              type: 'info',
              message: '已取消删除',
            })
          }
        },
      })
    } else if (row.type == 0) {
      // 递归验证下文件夹下有没有索引中的文件
      const getHasIndexing = (data) => {
        let hasIndexing = false
        data.forEach((item) => {
          if (item.type == 1 && item.fastgptStatusCn === '索引中') {
            hasIndexing = true
          } else if (item.type == 0) {
            hasIndexing = getHasIndexing(item.children)
          }
        })
        return hasIndexing
      }
      const hasIndexing = getHasIndexing(row.children)
      if (hasIndexing) {
        ElMessage({
          type: 'warning',
          message: '文件夹内有正在索引的文件，无法删除',
        })
        return
      }
      ElMessageBox.confirm('确认删除该文件夹及文件夹内所有数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            deleteFolder(row.bid)
              .then(() => {
                ElMessage({
                  type: 'success',
                  message: '删除成功!',
                })
              })
              .finally(() => {
                done()
                getTableData()
                instance.confirmButtonLoading = false
              })
          } else {
            done()
            ElMessage({
              type: 'info',
              message: '已取消删除',
            })
          }
        },
      })
    }
  }

  const showBatchBtns = ref(false)
  const selectedRows = ref([])
  function handleSelectionChange(rows) {
    selectedRows.value = rows
  }
  function handleBatchOperate(operate) {
    if (selectedRows.value.length === 0 && operate) {
      ElMessage({
        message: '请选择要操作的文件',
        type: 'warning',
      })
      return
    }

    showBatchBtns.value = operate
  }
  const batchDownloadLoading = ref(false)
  function handleBatchDownload() {
    console.log('批量下载', selectedRows.value)
    const ids = selectedRows.value.map((row) => row.bid)
    batchDownload(ids).then((res) => {
      downloadFileByBlob(res.data, '批量下载.zip')
    })
  }
  const batchDeleteLoading = ref(false)
  function handleBatchDelete() {
    console.log('批量删除', selectedRows.value)
    const hasFolder = selectedRows.value.some((row) => row.type == 0)

    const realRows = selectedRows.value.filter((row) => {
      return paths.value[paths.value.length - 1].bid
        ? row.superiorFolderBid === paths.value[paths.value.length - 1].bid
        : !row.superiorFolderBid
    })
    ElMessageBox.confirm(
      hasFolder ? '确认删除该文件夹及文件夹内所有数据？' : '确认删除该文件及其所有数据？',
      '提示',
      {
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const ids = realRows.map((row) => row.bid)
            batchDelete(ids).then(() => {
              ElMessage({
                message: '删除成功',
                type: 'success',
              })
              getTableData()
              done()
            })
          } else {
            done()
          }
        },
      },
    )
  }

  // const paginationData = reactive({
  //   pageSize: 10,
  //   pageNum: 1,
  //   total: 0,
  // })
  // 改变每页条数
  // function handlePageSizeChange() {
  //   paginationData.pageNum = 1
  //   getTableData()
  // }
  // 改变当前页
  // function handlePageNumChange() {
  //   getTableData()
  // }
</script>

<style lang="scss" scoped></style>
