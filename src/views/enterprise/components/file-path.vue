<template>
  <div class="file_path">
    <ElButton
      style="width: 32px"
      :plain="true"
      :text="true"
      :disabled="paths.length <= 1"
      @click="handlePrev">
      <template #icon>
        <i class="iconfont icon-qy_arrowup" />
      </template>
    </ElButton>
    <div class="file_path_items">
      <ElBreadcrumb :separator-icon="ArrowRight">
        <ElBreadcrumbItem
          v-for="item in paths"
          :key="item">
          <a @click="handleClick(item)">{{ item.name }}</a>
        </ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
  </div>
</template>

<script setup>
  import { ArrowRight } from '@element-plus/icons-vue'

  const { paths } = defineProps({
    paths: {
      type: Array,
      default: () => [],
    },
  })

  function handlePrev() {
    emit('change', paths[paths.length - 2])
  }

  const emit = defineEmits(['change'])
  function handleClick(item) {
    emit('change', item)
  }
</script>

<style lang="scss" scoped>
  .file_path {
    display: flex;
    align-items: center;
    height: 32px;

    &_btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 100%;
      border: none;
      background-color: transparent;
      color: #606266;
      cursor: pointer;
    }

    &_items {
      display: flex;
      flex: 1;
      align-items: center;
      height: 100%;
      margin-left: 12px;
      padding: 0 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }
</style>
