<template>
  <ElButton
    type="primary"
    :loading="uploadLoading"
    @click="handleUpload">
    <template #icon>
      <i class="iconfont icon-upload" />
    </template>
    上传文件
  </ElButton>
  <input
    ref="fileInput"
    type="file"
    style="display: none"
    accept=".docx,.doc,.pdf,.txt"
    @change="handleFileChange" />
</template>

<script setup>
  import { uploadFile } from '@/apis/enterprise'

  const { folderBid } = defineProps({
    folderBid: {
      type: String,
      default: '',
    },
  })

  const fileInput = useTemplateRef('fileInput')

  function handleUpload() {
    fileInput.value.click()
  }

  const emit = defineEmits(['change'])
  const uploadLoading = ref(false)
  function handleFileChange() {
    const files = fileInput.value.files

    if (!files.length) {
      return
    }

    // 仅支持.docx .pdf .txt格式文件上传
    if (!files[0].name.match(/\.docx|\.doc|\.pdf|\.txt$/)) {
      ElMessage.error('仅支持.docx .pdf .txt格式文件上传')
      return
    }

    uploadLoading.value = true
    uploadFile({
      file: files[0],
      folderBid: folderBid || '',
    })
      .then(() => {
        ElMessage.success('上传成功')
        emit('change', files)
      })
      .finally(() => {
        uploadLoading.value = false
      })
  }
</script>

<style lang="scss" scoped></style>
