<template>
  <ElButton
    :plain="true"
    @click="handleCreate">
    <template #icon>
      <i class="iconfont icon-plus" />
    </template>
    新建文件夹
  </ElButton>

  <ElDialog
    v-model="dialogVisible"
    width="480"
    title="新建文件夹"
    @close="handleCancel">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px">
      <ElFormItem
        prop="folderName"
        label="文件夹名称">
        <ElInput
          v-model="formData.folderName"
          placeholder="请输入文件夹名称"
          :minlength="1"
          :maxlength="20"
          :show-word-limit="true" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton
        type="primary"
        :loading="confirmLoading"
        @click="handleConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { createFolder } from '@/apis/enterprise'

  const { parentFolderBid } = defineProps({
    parentFolderBid: {
      type: String,
      default: '',
    },
  })

  const dialogVisible = ref(false)
  const formRef = useTemplateRef('formRef')
  const formData = reactive({
    folderName: '',
  })
  const formRules = reactive({
    folderName: [
      { required: true, message: '请输入文件夹名称', trigger: ['blur', 'change'] },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\uff00-\uffef\x21-\x7e]{1,20}$/,
        message: '文件夹名称仅支持中文、英文、数字、符号（除空格）且长度在1-20个字符之间',
        trigger: ['blur', 'change'],
      },
    ],
  })

  function handleCreate() {
    dialogVisible.value = true
  }

  function handleCancel() {
    dialogVisible.value = false

    formRef.value.resetFields()
  }

  const emit = defineEmits(['change'])
  const confirmLoading = ref(false)
  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        // dialogVisible.value = false
        // emit('change', formData.folderName)
        confirmLoading.value = true
        createFolder({
          folderName: formData.folderName,
          parentFolderBid: parentFolderBid || '',
        })
          .then(() => {
            handleCancel()
            emit('change', formData.folderName)
            ElMessage.success('创建成功')
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            confirmLoading.value = false
          })
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }
</script>

<style lang="scss" scoped></style>
