<template>
  <ElDialog
    v-model="visible"
    :title="type === 'create' ? '添加邮箱' : '编辑邮箱'"
    width="448px"
    @open="handleOpen">
    <ElAlert
      title="请添加绑定招聘平台收集简历的邮箱。"
      type="primary"
      :show-icon="true"
      :closable="false" />
    <div class="mt-[16px]">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rulesData"
        label-width="110px"
        label-position="right">
        <!-- 邮箱作用，固定为接收邮件 -->
        <ElFormItem
          label="邮箱作用"
          prop="emailFunction">
          <ElSelect
            v-model="formData.emailFunction"
            placeholder="请选择邮箱作用"
            :disabled="true">
            <ElOption
              v-for="item in EMAIL_USAGE"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <!-- 邮箱服务器 -->
        <ElFormItem prop="emailAddress">
          <template #label>
            <span>邮箱服务器</span>
            <ElTooltip
              placement="top"
              content="接收邮件请填写IMAP服务器地址">
              <i class="iconfont icon-info-filled ml-[4px] cursor-pointer text-[12px]!" />
            </ElTooltip>
          </template>
          <ElInput
            v-model="formData.emailAddress"
            placeholder="请输入邮箱服务器" />
        </ElFormItem>
        <!-- 邮箱账号 -->
        <ElFormItem
          label="邮箱账号"
          prop="emailAccount">
          <ElInput
            v-model="formData.emailAccount"
            placeholder="请输入邮箱账号" />
        </ElFormItem>
        <!-- 邮箱密码 -->
        <ElFormItem
          label="邮箱密码"
          prop="emailPassword">
          <ElInput
            v-model="formData.emailPassword"
            placeholder="请输入邮箱密码"
            :show-password="true" />
        </ElFormItem>
        <!-- SSL 端口, 默认993 -->
        <ElFormItem
          label="SSL 端口"
          prop="sslPort">
          <ElInput
            v-model="formData.sslPort"
            placeholder="请输入SSL端口"
            :disabled="true" />
        </ElFormItem>
        <!-- 发件人姓名 -->
        <ElFormItem
          label="发件人姓名"
          prop="senderName">
          <ElInput
            v-model="formData.senderName"
            placeholder="请输入发件人姓名" />
        </ElFormItem>
        <!-- 邮箱目的 -->
        <ElFormItem
          label="邮箱目的"
          prop="emailPurpose">
          <ElSelect
            v-model="formData.emailPurpose"
            placeholder="请选择邮箱目的"
            :disabled="true">
            <ElOption
              v-for="item in EMAIL_PURPOSE"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <!-- 关联渠道 -->
        <ElFormItem
          label="关联渠道"
          prop="relatedChannel">
          <ElSelect
            v-model="formData.relatedChannel"
            placeholder="请选择关联渠道"
            :disabled="true">
            <ElOption
              label="BOSS直聘"
              value="BOSS直聘" />
          </ElSelect>
        </ElFormItem>
      </ElForm>
    </div>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton
        type="primary"
        :loading="confirmLoading"
        @click="handleConfirm">
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { addEmail, updateEmail } from '@/apis/email'
  import { useDict } from '@/stores/dict'

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  const { type, row } = defineProps({
    type: {
      type: String,
      default: 'create',
      validator: (value) => ['create', 'edit'].includes(value),
    },
    row: {
      type: Object,
      default: () => ({}),
    },
  })

  const { EMAIL_PURPOSE, EMAIL_USAGE } = useDict('EMAIL_PURPOSE', 'EMAIL_USAGE')

  watchEffect(() => {
    if (EMAIL_PURPOSE.value.length) {
      formData.emailPurpose = EMAIL_PURPOSE.value[0].value
    }
    if (EMAIL_USAGE.value.length) {
      formData.emailFunction = EMAIL_USAGE.value[0].value
    }
  })

  const emit = defineEmits(['success'])

  const formData = reactive({
    emailFunction: '',
    emailAddress: '',
    emailAccount: '',
    emailPassword: '',
    sslPort: 993,
    senderName: '',
    emailPurpose: '',
    relatedChannel: 'BOSS直聘',
  })

  const rulesData = reactive({
    emailFunction: [{ required: true, message: '请选择邮箱作用', trigger: ['blur', 'change'] }],
    emailAddress: [{ required: true, message: '请输入邮箱服务器', trigger: ['blur', 'change'] }],
    emailAccount: [
      { required: true, message: '请输入邮箱账号', trigger: ['blur', 'change'] },
      {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '请输入正确的邮箱账号',
        trigger: ['blur', 'change'],
      },
    ],
    emailPassword: [{ required: true, message: '请输入邮箱密码', trigger: ['blur', 'change'] }],
    sslPort: [{ required: true, message: '请输入SSL端口', trigger: ['blur', 'change'] }],
    senderName: [{ required: true, message: '请输入发件人姓名', trigger: ['blur', 'change'] }],
    emailPurpose: [{ required: true, message: '请选择邮箱目的', trigger: ['blur', 'change'] }],
    relatedChannel: [{ required: true, message: '请选择关联渠道', trigger: ['blur', 'change'] }],
  })

  function handleOpen() {
    formRef.value.resetFields()
    if (type === 'edit') {
      for (const key in formData) {
        if (Object.prototype.hasOwnProperty.call(formData, key)) {
          formData[key] = row[key]
        }
      }
    }
  }

  function handleCancel() {
    visible.value = false
    formRef.value.resetFields()
  }

  const formRef = useTemplateRef('formRef')
  const confirmLoading = ref(false)
  function handleConfirm() {
    formRef.value.validate((valid) => {
      if (valid) {
        confirmLoading.value = true
        if (type === 'create') {
          addEmail(formData)
            .then(() => {
              ElMessage.success('添加成功')
              emit('success')
              visible.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          updateEmail({ ...formData, bid: row.bid })
            .then(() => {
              ElMessage.success('修改成功')
              emit('success')
              visible.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }
</script>

<style lang="scss" scoped></style>
