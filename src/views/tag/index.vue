<template>
  <div class="page">
    <div class="leftBox">
      <div
        class="title"
        style="margin-left: 10px">
        标签分类
      </div>
      <div class="categoryBox">
        <!-- <template v-if="true">
          <div
            :class="`categoryItem ${
              selectedTag.includes('light') ? 'categoryItemActive' : ''
            }`"
            @click="onChangeShowTags">
            <div class="icon_box">
              <i
                class="iconfont icon-down flex text-[12px]"
                :style="{
                  transition: 'all 0.5s',
                  transform: isShowChild ? 'rotate(0deg)' : 'rotate(-90deg)',
                }" />
            </div>
            <span
              :class="`category ${
                selectedTag.includes('light') ? 'categoryActive' : ''
              }`">
              亮点标签
            </span>
            <span
              :class="`categoryType ${
                selectedTag.includes('light') ? 'categoryTypeActive' : ''
              }`">
              系统
            </span>
          </div>
          <ElCollapseTransition>
            <div v-show="isShowChild">
              <div
                v-for="item in lightTagsList"
                :key="item.name"
                class="child"
                :class="`${
                  selectedTag === item.key ? 'child_categoryItemActive' : ''
                }`"
                @click="onSelectTag(item.key)">
                <span class="child_category">{{ item.name }}</span>
                <span
                  :class="`child_categoryType ${
                    selectedTag === item.key ? 'child_categoryTypeActive' : ''
                  }`">
                  系统
                </span>
              </div>
            </div>
          </ElCollapseTransition>
        </template> -->

        <div
          v-for="(item, index) in tagList"
          :key="index">
          <!-- 亮点标签 -->
          <template
            v-if="Array.isArray(item.children) && item.children.length > 0">
            <!-- 父级标题 -->
            <div
              :class="`categoryItem ${
                selectedTag.includes(item.bid) ? 'categoryItemActive' : ''
              }`"
              @click="onShowTags = item.name">
              <div class="icon_box">
                <i
                  class="iconfont icon-down flex text-[12px]!"
                  :style="{
                    transition: 'all 0.5s',
                    transform: onShowTags[item.name]
                      ? 'rotate(0deg)'
                      : 'rotate(-90deg)',
                  }" />
              </div>
              <span
                :class="`category ${
                  selectedTag.includes(item.bid) ? 'categoryActive' : ''
                }`">
                {{ item.name }}
              </span>
              <span
                :class="`categoryType ${
                  selectedTag.includes(item.bid) ? 'categoryTypeActive' : ''
                }`">
                系统
              </span>
            </div>
            <!-- 子集标题 -->
            <ElCollapseTransition>
              <div v-show="onShowTags[item.name]">
                <div
                  v-for="childTag in item.children"
                  :key="childTag.name"
                  class="child"
                  :class="`${
                    selectedTag === childTag.bid
                      ? 'child_categoryItemActive'
                      : ''
                  }`"
                  @click="onSelectTag(childTag.bid)">
                  <span class="child_category">{{ childTag.name }}</span>
                  <span
                    :class="`child_categoryType ${
                      selectedTag === childTag.bid
                        ? 'child_categoryTypeActive'
                        : ''
                    }`">
                    系统
                  </span>
                </div>
              </div>
            </ElCollapseTransition>
          </template>
          <!-- 基础标签 -->
          <template v-else>
            <div
              :class="`categoryItem ${
                selectedTag === item.bid ? 'categoryItemActive' : ''
              }`"
              @click="onSelectTag(item.bid)">
              <span
                :class="`category ${
                  selectedTag === item.bid ? 'categoryActive' : ''
                }`">
                {{ item.name }}
              </span>
              <span
                :class="`categoryType ${
                  selectedTag === item.bid ? 'categoryTypeActive' : ''
                }`">
                系统
              </span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="rightBox">
      <div class="title">
        <span>分类列表</span>
        <span class="subTitle">( {{ title }} )</span>
      </div>
      <ElTable
        :data="tableData"
        :border="true"
        style="width: 100%">
        <ElTableColumn
          label="序号"
          prop="index"
          width="90">
          <template #default="{ $index }">{{ $index + 1 }}</template>
        </ElTableColumn>
        <ElTableColumn
          label="标签名称"
          prop="name">
          <template #default="{ row }">
            <div
              class="tag"
              :style="{
                color,
              }">
              {{ row.name }}
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn label="标签类型">固定标签</ElTableColumn>
        <ElTableColumn label="创建人">admin</ElTableColumn>
        <ElTableColumn
          label="创建时间"
          prop="updatedAt" />
      </ElTable>
    </div>
  </div>
</template>

<script setup>
  import * as apis from '@/apis/tags'
  import { colors } from '@/views/tag/config'

  const selectedTag = ref('')
  const tableData = ref([])
  const tagList = ref([])
  onMounted(async () => {
    await getTagsList()
    onSearch()
  })

  const getTagsList = async () => {
    try {
      const res = await apis.getTagsList()
      const list = res.data.data
      const arr = list.map((item) => {
        if (item.children && item.children.length > 0) {
          return {
            ...item,
            show: false,
            children: item.children.map((child) => {
              return {
                ...child,
                bid: `${item.bid}_${child.bid}`,
              }
            }),
          }
        } else {
          return item
        }
      })
      selectedTag.value = arr[0]?.children[0]?.bid || ''
      tagList.value = arr
    } catch (error) {
      console.log(error)
    }
  }
  const onSearch = async () => {
    try {
      const bid = selectedTag.value.split('_').pop()
      const res = await apis.getTagsCategory_list({ bid })
      tableData.value = res.data.data
    } catch (error) {
      console.log(error)
    }
  }
  const onShowTags = computed({
    get() {
      const showObj = {}
      for (const key in tagList.value) {
        if (
          tagList.value[key].children &&
          Array.isArray(tagList.value[key].children) &&
          tagList.value[key].children.length > 0
        ) {
          showObj[tagList.value[key].name] = tagList.value[key].show
        }
      }
      return showObj
    },
    set(name) {
      for (const index in tagList.value) {
        if (tagList.value[index].name === name) {
          tagList.value[index].show = !tagList.value[index].show
        }
      }
    },
  })
  const title = computed(() => {
    const find = (tagList, tag) => {
      for (const key in tagList) {
        if (tagList[key].bid === tag) {
          return tagList[key].name
        }
        if (tagList[key].children && Array.isArray(tagList[key].children)) {
          const findName = find(tagList[key].children, tag)
          if (findName) {
            return findName
          }
        }
      }
      return ''
    }
    const name = find(tagList.value, selectedTag.value)
    return name
  })
  const color = computed(() => {
    const data = colors.find((item) => item.name === title.value)
    return data ? data.color : '#409EFF'
  })
  const onSelectTag = (tag) => {
    selectedTag.value = tag
    onSearch()
  }
</script>

<style lang="scss" scoped>
  .page {
    display: flex;
    height: 100%;
    gap: 16px;

    .leftBox {
      width: 189px;
      padding: 20px 10px;
      overflow: hidden;
      background: #fff;

      .categoryBox {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .icon_box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 14px;
          height: 14px;

          &:hover {
            background: rgb(0 85 255 / 6%);
          }
        }

        .categoryItem {
          display: flex;
          position: relative;
          align-items: center;
          justify-content: space-between;
          height: 34px;
          padding: 0 10px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.5s;

          &:hover {
            background: rgb(0 85 255 / 6%);
          }

          .deco {
            display: block;
            position: absolute;
            top: 0;
            left: -13px;
            width: 6px;
            height: 100%;
            border-radius: 3px;
            background: #05f;
          }

          .category {
            color: #303133;
            font-size: 14px;
          }

          .categoryActive {
            color: #05f;
          }

          .categoryType {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 22px;
            border: 1px solid #e4e7ed;
            border-radius: 11px;
            background: #fff;
            color: #909399;
            font-size: 12px;
          }

          .categoryTypeActive {
            border: 0;
            background: #05f;
            color: #fff;
            transition: all 0.5s;
          }
        }

        .categoryItemActive {
          background: rgb(0 85 255 / 6%);
        }

        .child {
          display: flex;
          column-gap: 45px;
          align-items: center;
          justify-content: flex-end;
          height: 34px;
          padding-right: 8px;
          cursor: pointer;
          transition: all 0.5s;

          &_category {
            font-size: 14px;
          }

          &_categoryType {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 11px;
            background: #fff;
            color: #909399;
            font-size: 12px;
          }

          &_categoryTypeActive {
            border: 0;
            background: #05f;
            color: #fff;
            transition: all 0.5s;
          }

          &:hover {
            background: rgb(0 85 255 / 6%);
          }
        }

        .child_categoryItemActive {
          background: rgb(0 85 255 / 6%);
        }
      }
    }

    .rightBox {
      display: flex;
      flex-direction: column;
      width: calc(100% - 189px);
      height: 100%;
      padding: 20px;
      background: #fff;

      .tag {
        display: inline-block;
        height: 24px;
        padding: 0 10px;
        border-radius: 4px;
        background: #f5f7fa;
        font-size: 12px;
      }
    }

    .title {
      margin-bottom: 20px;
      color: #303133;
      font-weight: 600;
      font-size: 20px;

      .subTitle {
        color: #c0c4cc;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  :deep(.el-table__header th) {
    background: #f5f7fa;
  }
</style>
