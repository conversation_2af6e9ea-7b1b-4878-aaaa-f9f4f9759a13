<template>
  <ElDialog
    v-model="visible"
    title=""
    width="700px"
    :style="{ '--el-dialog-padding-primary': '0px' }"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="handleOpen">
    <p class="mb-[24px] pt-[60px] text-center text-[22px] font-[600] text-[#303133]">
      BOSS直聘扫码登录
    </p>
    <div class="mb-[64px] flex justify-center">
      <ElImage
        style="width: 176px; height: 176px"
        :src="qrCodeUrl"
        alt=""
        :preview-src-list="[]" />
    </div>
    <template #footer>
      <ElAlert
        title="请使用与飞书手机号码一致的Boss直聘账号进行登录"
        type="primary"
        :show-icon="true"
        :closable="false"
        :center="true" />
    </template>
  </ElDialog>
</template>

<script setup>
  import { getBossQrCode, loginBoss } from '@/apis/channel'

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  const emit = defineEmits(['success'])

  function handleOpen() {
    getQrCode()
  }

  const baseQrUrl = ref('')
  const controller = ref(null)
  function getQrCode() {
    controller.value = new AbortController()
    loginBoss(null, controller.value.signal)
      .then((res) => {
        if (res === '登录成功') {
          ElNotification({
            title: '登录成功',
            message: 'BOSS直聘登录成功',
            type: 'success',
            duration: 0,
          })
          emit('success')
        } else {
          ElNotification({
            title: '登录失败',
            message: res,
            type: 'error',
            duration: 0,
          })
        }
      })
      .catch((err) => {
        if (err.message === '接口请求超时') {
          ElNotification({
            title: '提示',
            message: '长时间未操作，请重新登录',
            type: 'error',
            duration: 0,
          })
        }
        if (err.message === '请求取消') {
          ElNotification({
            title: '提示',
            message: '登录取消',
            type: 'error',
            duration: 0,
          })
        }
      })
      .finally(() => {
        visible.value = false
      })

    getBossQrCode()
      .then((res) => {
        baseQrUrl.value = res
        refreshQrCode()
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const qrCodeUrl = ref('')

  // 添加时间戳防止缓存
  function addTimestamp(url) {
    return url ? `${url}?t=${new Date().getTime()}` : ''
  }

  // 刷新二维码
  function refreshQrCode() {
    const newUrl = addTimestamp(baseQrUrl.value)
    qrCodeUrl.value = newUrl
  }

  // 定时器引用
  const refreshTimer = ref(null)
  // 监听对话框显示状态
  watchEffect(() => {
    if (visible.value) {
      refreshTimer.value = setInterval(refreshQrCode, 2000) // 2秒刷新一次
    } else {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
        refreshTimer.value = null
      }

      if (controller.value) {
        controller.value.abort()
        controller.value = null
      }
    }
  })

  // 组件卸载时取消请求
  onUnmounted(() => {
    if (controller.value) {
      controller.value.abort()
      controller.value = null
    }
  })

  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (!refreshTimer.value) return

    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  })
</script>

<style lang="scss" scoped></style>
