<template>
  <div class="channel">
    <PageHeader
      content="渠道管理"
      :has-back="false" />
    <div class="mt-[20px] flex flex-wrap gap-[16px]">
      <div
        v-for="item in channelList"
        :key="item.name"
        v-loading="item.loading"
        class="channel_item"
        :class="[item.loginStatus ? 'connected' : 'disconnected']"
        @click="handleLogin(item)">
        <img
          class="h-full w-full"
          :src="item.image"
          alt="" />
        <div class="channel_item_mask">
          <template v-if="!item.loginStatus">
            <p
              v-if="item.disabled"
              class="channel_item_name">
              暂不支持，敬请期待！
            </p>
            <p
              v-else
              class="channel_item_name">
              登录
              <i class="iconfont icon-right ml-[4px] text-[12px]!" />
            </p>
          </template>
        </div>
        <template v-if="!item.disabled">
          <p
            v-if="item.loginStatus"
            class="channel_item_status">
            <span class="channel_item_status_dot"></span>
            已连接
          </p>
          <p
            v-else
            class="channel_item_status">
            <span class="channel_item_status_dot"></span>
            未连接
          </p>
        </template>
      </div>
    </div>
  </div>

  <BossDialog
    v-model:visible="channelList[0].dialogVisible"
    @success="handleLoginSuccess" />
</template>

<script setup>
  import { checkBossLogin } from '@/apis/channel'
  import image_58 from '@/assets/images/channel/image_58.png'
  import image_boss from '@/assets/images/channel/image_boss.png'
  import image_lp from '@/assets/images/channel/image_lp.png'
  import image_qcwy from '@/assets/images/channel/image_qcwy.png'
  import image_zl from '@/assets/images/channel/image_zl.png'
  import { PageHeader } from '@/components'
  import BossDialog from './boss-dialog.vue'

  const channelList = ref([
    {
      name: 'BOSS直聘',
      image: image_boss,
      loading: false,
      loginStatus: false,
      disabled: false,
      dialogVisible: false,
    },
    {
      name: '智联招聘',
      image: image_zl,
      disabled: true,
    },
    {
      name: '前程无忧',
      image: image_qcwy,
      disabled: true,
    },
    {
      name: '猎聘',
      image: image_lp,
      disabled: true,
    },
    {
      name: '58同城·招聘',
      image: image_58,
      disabled: true,
    },
  ])

  function handleLogin(item) {
    if (item.disabled) return

    if (item.loginStatus) {
      return
    }

    item.dialogVisible = true
  }

  function checkLoginStatus() {
    channelList.value[0].loading = true
    checkBossLogin()
      .then((res) => {
        channelList.value[0].loginStatus = res === '已登录'
      })
      .finally(() => {
        channelList.value[0].loading = false
      })
  }

  onMounted(() => {
    checkLoginStatus()
  })

  function handleLoginSuccess() {
    checkLoginStatus()
  }
</script>

<style lang="scss" scoped>
  .channel {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    overflow: auto;
    border-radius: 4px;
    background-color: #fff;

    &_item {
      display: flex;
      position: relative;
      width: 310px;
      height: 200px;
      cursor: pointer;
      user-select: none;

      &_mask {
        display: flex;
        position: absolute;
        top: 0;
        left: 0;
        flex-direction: column;
        justify-content: flex-end;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      &_name {
        height: 34px;
        background: rgba(0, 0, 0, 0.2);
        color: #fff;
        font-size: 14px;
        line-height: 34px;
      }

      &_status {
        display: flex;
        position: absolute;
        top: 6px;
        right: 8px;
        align-items: center;
        font-weight: 600;
        font-size: 12px;

        &_dot {
          display: inline-block;
          width: 6px;
          height: 6px;
          margin-right: 4px;
          border-radius: 50%;
        }
      }

      &.connected {
        cursor: default;
        .channel_item_mask {
          border: 2px solid #67c23a;
          background: rgba(78, 194, 58, 0.1);
        }
        .channel_item_name {
          color: #4ec23a;
        }
        .channel_item_status {
          color: #67c23a;
        }
        .channel_item_status_dot {
          background: #67c23a;
        }
      }

      &.disconnected {
        &:hover {
          .channel_item_mask {
            background: rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
          }
        }
        &:active {
          .channel_item_mask {
            background: rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
          }
        }
        .channel_item_status {
          color: #f56c6c;
        }
        .channel_item_status_dot {
          background: #f56c6c;
        }
      }
    }
  }
</style>
