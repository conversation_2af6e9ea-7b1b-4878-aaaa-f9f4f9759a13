<template>
  <VChart
    :option="option"
    :loading="loading"
    :autoresize="true" />
</template>

<script setup>
  const { data, loading } = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const image = new URL('@/assets/images/home/<USER>', import.meta.url).href
  const option = reactive({
    color: ['#0055FF', '#FF7E86'],
    legend: {
      bottom: 8,
      left: 'center',
      icon: 'circle',
    },
    graphic: {
      elements: [
        {
          type: 'image',
          left: 'center',
          top: 55,
          style: {
            image,
            width: 124,
            height: 124,
          },
        },
      ],
    },
    series: [
      {
        name: 'pie',
        type: 'pie',
        top: 17,
        left: 'center',
        width: 200,
        height: 200,
        radius: [100 - 30, 100],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
        },
        label: {
          show: true,
          formatter: '{b}：{d}%',
          color: '#606266',
        },
        data: [
          // { value: 80, name: '成功率' },
          // { value: 20, name: '失败率' },
        ],
      },
    ],
  })

  watch(
    () => [data.successCount, data.failCount],
    (nv) => {
      console.log('nv', nv)
      option.series[0].data = [
        { value: nv[0], name: '成功率' },
        { value: nv[1], name: '失败率' },
      ]
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped></style>
