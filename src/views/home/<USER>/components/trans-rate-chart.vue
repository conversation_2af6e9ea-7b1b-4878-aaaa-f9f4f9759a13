<template>
  <div class="flex h-full w-full">
    <div class="flex flex-1 flex-col items-center justify-center">
      <div class="scene h-[148px] w-[148px]">
        <VChart
          :option="option1"
          :loading="loading"
          :autoresize="true" />
        <div class="shadow"></div>
      </div>

      <p class="mt-[24px] w-[148px] text-center text-[14px] text-[#606266]">
        HR主动打招呼获取简历转化率
      </p>
    </div>
    <div class="flex flex-1 flex-col items-center justify-center">
      <div class="scene h-[148px] w-[148px]">
        <VChart
          :option="option2"
          :loading="loading"
          :autoresize="true" />
        <div class="shadow"></div>
      </div>
      <p class="mt-[24px] w-[148px] text-center text-[14px] text-[#606266]">
        HR被动打招呼获取简历转化率
      </p>
    </div>
  </div>
</template>

<script setup>
  import { graphic } from 'echarts'

  const { data, loading } = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const baseOption = {
    type: 'liquidFill',
    radius: 148,
    top: 40,
    left: 'center',
    data: [0, 0], // data个数代表波浪数
    color: [
      // {
      //   type: 'linear',
      //   x: 0,
      //   y: 0,
      //   x2: 0,
      //   y2: 1,
      //   colorStops: [
      //     {
      //       offset: 0,
      //       color: '#21D0FF',
      //     },
      //     {
      //       offset: 1,
      //       color: '#0055FF',
      //     },
      //   ],
      // },
      new graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#21D0FF',
        },
        {
          offset: 1,
          color: '#0055FF',
        },
      ]),
    ],
    backgroundStyle: {
      color: new graphic.RadialGradient(0.5, 0.5, 1, [
        {
          offset: 0,
          color: 'rgba(0, 85, 255, 0)',
        },
        {
          offset: 1,
          color: 'rgba(0, 85, 255, 0.20)',
        },
      ]),
    },
    outline: {
      show: false,
    },
    itemStyle: {
      shadowBlur: 0,
      shadowColor: 'rgba(0,0,0,0)',
    },
    label: {
      formatter: (params) => {
        return (params.value * 100).toFixed(0) + '%'
      },
      textStyle: {
        fontSize: 36,
        color: '#00A6FF',
      },
    },
  }

  const option1 = reactive({
    series: [
      {
        ...baseOption,
        data: [0, 0],
      },
    ],
  })

  watch(
    () => [data.activeGreetCount, data.activeResumeCount],
    (nv) => {
      // HR主动获取简历/HR主动打招呼总次数*100%
      const rate = nv[0] <= 0 ? 0 : nv[1] / nv[0]
      option1.series[0].data = [rate, rate]
    },
    {
      immediate: true,
    },
  )

  const option2 = reactive({
    series: [
      {
        ...baseOption,
        data: [0, 0],
      },
    ],
  })

  watch(
    () => [data.passiveGreetCount, data.passiveResumeCount],
    (nv) => {
      // HR被动获取简历/HR被动打招呼总次数*100%
      const rate = nv[0] <= 0 ? 0 : nv[1] / nv[0]
      option2.series[0].data = [rate, rate]
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped>
  .scene {
    position: relative;
    perspective: 600px;
    .shadow {
      z-index: 1;
      position: absolute;
      bottom: -20px;
      left: 50%;
      width: 110px;
      height: 12px;
      transform: translateX(-50%) rotateX(80deg) /* 关键变形：模拟地面透视 */ scaleY(3); /* 压扁阴影 */
      border-radius: 50%;
      background: rgba($color: #0055ff, $alpha: 0.3);
      filter: blur(10px);
    }
  }
</style>
