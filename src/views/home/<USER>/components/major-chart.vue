<template>
  <VChart
    :option="option"
    class="major"
    :autoresize="true" />
</template>

<script setup>
  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const option = computed(() => ({
    title: {
      subtext: props.title,
      left: 'center',
      bottom: '0',
    },
    tooltip: {
      confine: true,
      borderColor: 'transparent',
      formatter(param) {
        // console.log(param)
        const majorNameList = []

        const recursion = (data) => {
          if (data.children) {
            data.children.forEach((item) => {
              recursion(item)
            })
          } else {
            majorNameList.push({
              name: data.name,
              value: data.value,
            })
          }
        }
        recursion(param.data)

        const total = majorNameList.reduce(
          (total, item) => total + item.value,
          0,
        )

        return `
            <div style="width:100%">
              <div style="margin-bottom:${
                total === majorNameList[0].value ? 0 : '6px'
              }">
                <span style="font-size: 12px;color: ${
                  param.color
                };margin-right: 20px;">${param.data.name || ''}总人数</span>
                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">
                  ${total}人
                </span>
              </div>
              <div style="display: ${
                total === majorNameList[0].value ? 'none' : 'flex'
              };flex-wrap: wrap;font-size: 12px;max-height:100%;overflow-y:auto;gap:4px 10px;">
                ${majorNameList
                  .map(
                    (item) => `
                              <div style="min-width: 165px;display:flex;justify-content:space-between;align-items: center;">
                                <div style="display:flex;align-items: center;">
                                  <span style="width: 6px;height: 6px;background: ${param.color};border-radius: 50%;margin-right: 4px;"></span>
                                  <span style="color: rgba(0,0,0,0.45);">${item.name}</span>
                                </div>
                                <span style="font-weight: 600;font-size: 12px;color: rgba(0,0,0,0.88);">${item.value}人</span>
                              </div>
                            `,
                  )
                  .join('')}
              </div>
            </div>
          `
      },
    },
    series: {
      type: 'sunburst',
      data: props.chartData,
      radius: ['30%', '70%'],
      itemStyle: {
        borderWidth: 1,
      },
      label: {
        show: false,
      },
      emphasis: {
        focus: 'ancestor',
      },
    },
  }))
</script>

<style lang="scss" scoped>
  .major {
    width: 33.33%;
    height: 100%;
  }
</style>
