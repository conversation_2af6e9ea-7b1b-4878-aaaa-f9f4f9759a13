<template>
  <VChart
    :option="option"
    :loading="loading"
    :autoresize="true" />
</template>

<script setup>
  const { data, loading } = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const option = reactive({
    color: ['#0055FF', '#14C9C9', '#F7BA1E'],
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: 35,
      left: 5,
      right: 5,
      bottom: 30,
      containLabel: true,
    },
    legend: {
      top: 0,
      right: 0,
      icon: 'circle',
    },
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#C9CDD4',
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    dataZoom: {
      type: 'slider',
      height: 20,
      bottom: 10,
      backgroundColor: '#FAFAFA',
      dataBackground: {
        lineStyle: {
          color: '#EBEBEB',
        },
        areaStyle: {
          color: '#F0F0F0',
          opacity: 1,
        },
      },
      selectedDataBackground: {
        lineStyle: {
          color: '#77C6F2',
        },
        areaStyle: {
          color: '#C3E1F2',
          opacity: 1,
        },
      },
      fillColor: '#C3E1F2',
      borderColor: '#EBEBEB',
      borderRadius: 2,
      moveHandleStyle: {
        opacity: 0,
      },
      brushStyle: {
        color: '#E1F1FA',
      },
    },
    series: [
      {
        name: 'HR被动获取简历量',
        type: 'line',
        data: [],
      },
      {
        name: 'HR主动获取简历量',
        type: 'line',
        data: [],
      },
      {
        name: '手动获取简历量',
        type: 'line',
        data: [],
      },
    ],
  })

  watch(
    () => data,
    (nv) => {
      option.xAxis.data = nv.map((item) => item.date)
      option.series[0].data = nv.map((item) => item.hrPassiveResumeCount)
      option.series[1].data = nv.map((item) => item.hrActiveResumeCount)
      option.series[2].data = nv.map((item) => item.handResumeCount)
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped></style>
