<template>
  <VChart
    ref="wordCloud"
    :option="option"
    :autoresize="true"
    class="wordCloud" />
</template>

<script setup>
  import 'echarts-wordcloud'

  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
  })

  const wordCloud = ref()

  const option = reactive({
    title: {
      text: '求职岗位分布',
    },
    series: [
      {
        type: 'wordCloud',
        width: '100%',
        height: '100%',
        shape: 'square',
        top: 20,
        bottom: 0,
        gridSize: 6,
        sizeRange: [10, 26],
        rotationRange: [0, 0],
        drawOutOfBound: false,
        layoutAnimation: true,
        textStyle: {
          color: function () {
            return (
              'rgb(' +
              [
                Math.round(Math.random() * 255),
                Math.round(Math.random() * 255),
                Math.round(Math.random() * 255),
              ].join(',') +
              ')'
            )
          },
        },
        emphasis: {
          textStyle: {
            textShadowBlur: 3,
            textShadowColor: '#666',
          },
        },
        data: props.chartData || [],
      },
    ],
  })

  watch(
    () => props.chartData,
    (nv) => {
      option.series[0].data = nv
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped>
  .wordCloud {
    width: 100%;
    height: 100%;
  }
</style>
