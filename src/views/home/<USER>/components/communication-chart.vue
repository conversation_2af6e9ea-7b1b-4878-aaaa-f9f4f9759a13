<template>
  <VChart
    :option="option"
    :loading="loading"
    :autoresize="true" />
</template>

<script setup>
  const { data, loading } = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const option = reactive({
    color: ['#0055FF', '#14C9C9'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(0, 0, 0, 0.06)',
        },
      },
    },
    grid: {
      top: 0,
      left: 0,
      right: 10,
      bottom: 0,
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: ['HR主动招呼', 'HR被动获取简历', 'HR被动打招呼', 'HR主动获取简历'],
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#C9CDD4',
        },
      },
      inverse: true,
    },
    xAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: 24,
        data: [
          {
            value: 0,
            itemStyle: {
              color: '#0055FF',
            },
          },
          {
            value: 0,
            itemStyle: {
              color: '#14C9C9',
            },
          },
          {
            value: 0,
            itemStyle: {
              color: '#0055FF',
            },
          },
          {
            value: 0,
            itemStyle: {
              color: '#14C9C9',
            },
          },
        ],
      },
    ],
  })

  watch(
    () => [
      data.activeGreetCount, // HR主动打招呼
      data.passiveResumeCount, // HR被动获取简历
      data.passiveGreetCount, // HR被动打招呼
      data.activeResumeCount, // HR主动获取简历
    ],
    (nv) => {
      option.series[0].data = [
        {
          value: nv[0],
          itemStyle: {
            color: '#0055FF',
          },
        },
        {
          value: nv[1],
          itemStyle: {
            color: '#14C9C9',
          },
        },
        {
          value: nv[2],
          itemStyle: {
            color: '#0055FF',
          },
        },
        {
          value: nv[3],
          itemStyle: {
            color: '#14C9C9',
          },
        },
      ]
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped></style>
