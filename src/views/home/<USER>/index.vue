<template>
  <div class="dashboard">
    <div class="flex w-full gap-[16px]">
      <div class="mapBox">
        <div class="title">
          人才分布
          <span class="text-[#C0C4CC]">({{ mapTitle }})</span>
        </div>
        <div class="content">
          <div class="rankContainer">
            <div v-show="mapLevel < 3">
              <div class="rankBox">
                <img
                  alt=""
                  class="rankImg"
                  src="@/assets/images/rank1.png" />
                <div class="rankTop">
                  <span class="rankProvince">{{ rankList?.[0]?.name }}</span>
                  <span class="rank">/ Top1</span>
                </div>
                <div class="rankNum">
                  <span class="rankCount">
                    {{ numberSplit(rankList[0]?.value) }}
                  </span>
                  <span class="rankUnit">人</span>
                </div>
              </div>
              <div class="rankBox">
                <img
                  alt=""
                  class="rankImg"
                  src="@/assets/images/rank2.png" />
                <div class="rankTop">
                  <span class="rankProvince">{{ rankList?.[1]?.name }}</span>
                  <span class="rank">/ Top2</span>
                </div>
                <div class="rankNum">
                  <span class="rankCount">
                    {{ numberSplit(rankList[1]?.value) }}
                  </span>
                  <span class="rankUnit">人</span>
                </div>
              </div>
              <div class="rankBox">
                <img
                  alt=""
                  class="rankImg"
                  src="@/assets/images/rank3.png" />
                <div class="rankTop">
                  <span class="rankProvince">{{ rankList?.[2]?.name }}</span>
                  <span class="rank">/ Top3</span>
                </div>
                <div class="rankNum">
                  <span class="rankCount">
                    {{ numberSplit(rankList[2]?.value) }}
                  </span>
                  <span class="rankUnit">人</span>
                </div>
              </div>
            </div>

            <div class="legendBox">
              <div class="legendTitle">人数规模</div>
              <div class="legendContent">
                <div class="legendItem">
                  <div class="legend legend1" />
                  <div class="legendText">大于1000人</div>
                </div>
                <div class="legendItem">
                  <div class="legend legend2" />
                  <div class="legendText">501～1000人</div>
                </div>
                <div class="legendItem">
                  <div class="legend legend3" />
                  <div class="legendText">101～500人</div>
                </div>
                <div class="legendItem">
                  <div class="legend legend4" />
                  <div class="legendText">1～100人</div>
                </div>
                <div class="legendItem">
                  <div class="legend legend5" />
                  <div class="legendText">暂无人数</div>
                </div>
              </div>
            </div>
          </div>

          <WorldMap
            :start-date="startDate"
            :end-date="endDate"
            :position-name="positionName"
            :position-bid="positionBid"
            @map-change="onMapChange" />
        </div>
      </div>
      <div class="flex flex-col gap-[16px]">
        <div
          v-loading="loading.gender"
          class="smallBox">
          <div class="title">性别分布</div>
          <GenderChart :chart-data="genderData" />
          <div class="total">
            <div class="totalTitle">总人数</div>
            <div class="totalNum">
              {{ +genderData[0]?.value + +genderData[1]?.value }}
            </div>
          </div>
        </div>
        <div
          v-loading="loading.education"
          class="smallBox">
          <div class="title">学历分布</div>
          <EducationChart :chart-data="educationData" />
        </div>
      </div>
    </div>
    <div class="flex w-full gap-[16px]">
      <div
        v-loading="loading.age"
        class="mediumBox">
        <div
          class="title"
          style="position: absolute; top: 20px; left: 20px">
          年龄分布
        </div>
        <AgeChart :chart-data="ageData" />
      </div>
      <div
        v-loading="loading.school"
        class="mediumBox">
        <div
          class="title"
          style="position: absolute; top: 20px; left: 20px">
          学校分布
        </div>
        <SchoolChart :chart-data="schoolData" />
      </div>
      <div
        v-loading="loading.word"
        class="smallBox">
        <WordCloud :chart-data="wordData" />
      </div>
    </div>
    <!-- <div class="bottomBox">
      <div
        class="title"
        style="position: absolute; top: 20px; left: 20px">
        “5+9”相关产业专业分布
      </div>
      <MajorChart
        v-loading="loading.major"
        :chart-data="majorChartData1"
        title="专科专业分布" />
      <MajorChart
        v-loading="loading.major"
        :chart-data="majorChartData2"
        title="本科专业分布" />
      <MajorChart
        v-loading="loading.major"
        :chart-data="majorChartData3"
        title="研究生专业分布" />
    </div> -->
  </div>
</template>

<script setup>
  import AgeChart from './components/age-chart.vue'
  import EducationChart from './components/education-chart.vue'
  import GenderChart from './components/gender-chart.vue'
  // import MajorChart from './components/major-chart.vue'
  import SchoolChart from './components/school-chart.vue'
  import WordCloud from './components/word-cloud.vue'
  import WorldMap from './components/world-map.vue'

  import {
    getAge,
    getGender,
    // getMajor,
    getPosition,
    getQualification,
    getSchool,
  } from '@/apis/home/<USER>'

  import { useDict } from '@/stores/dict'
  import { numberSplit } from '@/utils'

  const { EDUCATION_LEVEL } = useDict('EDUCATION_LEVEL')

  const { startDate, endDate, positionName, positionBid } = defineProps({
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
    positionName: {
      type: String,
      default: '',
    },
    positionBid: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['update-query'])

  // 地图层级
  const mapLevel = ref(1)
  const mapTitle = ref('全国')

  // 排名
  const rankList = ref([])

  const loading = reactive({
    gender: true,
    education: true,
    school: true,
    age: true,
    word: true,
    // major: true,
  })

  function onMapChange({ name, level, data, params }) {
    mapLevel.value = level
    mapTitle.value = name === 'china' ? '全国' : name
    rankList.value = data.slice(0, 3)
    getGenderData(params)
    getQualificationData(params)
    getAgeData(params)
    getPositionData(params)
    // getMajorData(params)
    getSchoolData(params)

    emit('update-query')
  }

  // 性别分布
  const genderData = ref([])
  async function getGenderData(mapParams) {
    try {
      loading.gender = true
      const {
        data: { data: res },
      } = await getGender(mapParams)
      genderData.value = res
    } catch (error) {
      console.log(error)
    } finally {
      loading.gender = false
    }
  }

  // 学历分布
  const educationData = ref([])
  async function getQualificationData(mapParams) {
    try {
      loading.education = true
      const {
        data: { data: res },
      } = await getQualification(mapParams)

      educationData.value = res.map((item) => ({
        [EDUCATION_LEVEL.value.find((i) => i.value === item.name.toString())?.label]: item.name,
        value: item.value,
      }))
    } catch (error) {
      console.log(error)
    } finally {
      loading.education = false
    }
  }

  // 学校分布
  const schoolData = ref([])
  async function getSchoolData(mapParams) {
    try {
      loading.school = true
      const {
        data: { data: res },
      } = await getSchool(mapParams)
      schoolData.value = res
    } catch (error) {
      console.log(error)
    } finally {
      loading.school = false
    }
  }

  // 年龄分布
  const ageData = ref([])
  async function getAgeData(mapParams) {
    try {
      loading.age = true
      const {
        data: { data: res },
      } = await getAge(mapParams)
      ageData.value = res.map((item) => {
        const name = item.gender ? '女' : '男'
        const data = item.vos.map((i) => ({ name: i.name, value: i.value }))
        return {
          name,
          data,
        }
      })
    } catch (error) {
      console.log(error)
    } finally {
      loading.age = false
    }
  }

  // 词云
  const wordData = ref([])
  async function getPositionData(mapParams) {
    try {
      loading.word = true
      const {
        data: { data: res },
      } = await getPosition(mapParams)
      wordData.value = res
    } catch (error) {
      console.log(error)
    } finally {
      loading.word = false
    }
  }

  // 专业分布
  // const majorChartData1 = ref([])
  // const majorChartData2 = ref([])
  // const majorChartData3 = ref([])
  // async function getMajorData(mapParams) {
  //   try {
  //     loading.major = true
  //     const {
  //       data: { data: res },
  //     } = await getMajor(mapParams)
  //     majorChartData1.value = []
  //     majorChartData2.value = []
  //     majorChartData3.value = []
  //     res.forEach((item) => {
  //       if (item.name === '中专' || item.name === '大专') {
  //         majorChartData1.value.push(item)
  //       } else if (item.name === '本科') {
  //         majorChartData2.value = item.children
  //       } else {
  //         majorChartData3.value.push(...item.children)
  //       }
  //     })
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //     loading.major = false
  //   }
  // }
</script>

<style lang="scss" scoped>
  .dashboard {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: 100%;
    overflow: auto;
    gap: 16px;

    .mapBox {
      // width: 1300px;
      flex: 1;
      height: 662px;
      padding: 20px;
      border-radius: 4px;
      background: #fff;

      .content {
        display: flex;
        gap: 20px;

        .rankContainer {
          width: 270px;

          .rankBox {
            display: flex;
            position: relative;
            flex-direction: column;
            justify-content: space-between;
            width: 270px;
            height: 121px;
            margin-bottom: 16px;
            padding: 20px;
            border-radius: 4px;

            &:first-child {
              border: 1px solid #f9e798;
              background: rgb(249 231 152 / 20%);
            }

            &:nth-child(2) {
              background: rgb(207 227 253 / 20%);
            }

            &:nth-child(3) {
              background: rgb(244 219 190 / 20%);
            }

            .rankImg {
              position: absolute;
              right: 0;
              bottom: 0;
              width: 71px;
              height: 81px;
            }

            .rankTop {
              .rankProvince {
                color: #606266;
                font-size: 18px;
              }

              .rank {
                margin-left: 6px;
                color: #c0c4cc;
                font-size: 14px;
              }
            }

            .rankNum {
              .rankCount {
                color: #303133;
                font-weight: 500;
                font-size: 30px;
              }

              .rankUnit {
                margin-left: 4px;
                color: #909399;
                font-size: 14px;
              }
            }
          }

          .legendBox {
            height: 138px;
            margin-top: 40px;

            .legendTitle {
              margin-bottom: 16px;
              color: #303133;
              font-weight: 500;
              font-size: 16px;
            }

            .legendContent {
              display: flex;
              flex-wrap: wrap;

              .legendItem {
                display: flex;
                align-items: center;
                width: 50%;
                margin-bottom: 16px;

                .legend {
                  width: 10px;
                  height: 10px;
                  margin-right: 8px;
                  border-radius: 50%;
                }

                .legend1 {
                  background: #2d5cbc;
                }

                .legend2 {
                  background: #4e8cec;
                }

                .legend3 {
                  background: #62acfb;
                }

                .legend4 {
                  background: #91cbfb;
                }

                .legend5 {
                  background: #e8f3ff;
                }

                .legendText {
                  color: rgb(0 0 0 / 65%);
                  font-weight: 500;
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }

    .smallBox {
      position: relative;
      flex: none;
      width: 356px;
      height: 323px;
      margin-left: auto;
      padding: 20px;
      border-radius: 4px;
      background: #fff;

      .total {
        position: absolute;
        top: 55%;
        left: 50%;
        width: 85px;
        height: 60px;
        transform: translate(-50%, -50%);
        text-align: center;

        .totalTitle {
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
        }

        .totalNum {
          color: #05f;
          font-weight: 700;
          font-size: 26px;
        }
      }
    }

    .mediumBox {
      position: relative;
      // width: 643px;
      flex: 1;
      height: 323px;
      padding: 20px;
      border-radius: 4px;
      background: #fff;
    }

    .bottomBox {
      display: flex;
      position: relative;
      justify-content: space-between;
      width: 100%;
      height: 340px;
      // margin-right: 10px;
      border-radius: 4px;
      background: #fff;
    }

    .title {
      z-index: 9999;
      margin-bottom: 20px;
      color: #303133;
      font-weight: 600;
      font-size: 20px;
    }
  }
</style>
