<template>
  <div class="home">
    <div class="home_header">
      <div class="picker">
        <div
          class="picker_item"
          :class="{ active: searchData.tab === '招聘看板' }"
          @click="handleTabChange('招聘看板')">
          招聘看板
        </div>
        <span class="picker_separator">/</span>
        <div
          class="picker_item"
          :class="{ active: searchData.tab === '人才库看板' }"
          @click="handleTabChange('人才库看板')">
          人才库看板
        </div>
      </div>
      <div class="flex gap-[24px]">
        <ElDatePicker
          v-model="date"
          style="width: 280px"
          type="daterange"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期" />
        <PositionNameSelect
          ref="positionNameSelectRef"
          v-model:position-name="searchData.positionName"
          v-model:position-bid="searchData.positionBid"
          model-type="positionBid"
          style="width: 280px" />
      </div>
    </div>
    <div class="home_content">
      <Recruitment
        v-if="searchData.tab === '招聘看板'"
        :position-name="searchData.positionName"
        :position-bid="searchData.positionBid"
        :start-date="searchData?.startDate || ''"
        :end-date="searchData?.endDate || ''"
        @update-query="updateRouteQuery" />
      <TalentPool
        v-if="searchData.tab === '人才库看板'"
        :position-name="searchData.positionName"
        :position-bid="searchData.positionBid"
        :start-date="searchData?.startDate || ''"
        :end-date="searchData?.endDate || ''"
        @update-query="updateRouteQuery" />
    </div>
  </div>
</template>

<script setup>
  import { PositionNameSelect } from '@/components'
  import { useRouteQuery } from '@/hooks/usePageQuery'
  import Recruitment from './recruitment/index.vue'
  import TalentPool from './talent-pool/index.vue'

  // const activeTab = ref('招聘看板')

  function handleTabChange(tab) {
    if (searchData.tab === tab) {
      return
    }
    searchData.tab = tab

    updateRouteQuery()
  }

  // const searchData = reactive({
  //   date: [],
  //   positionName: '',
  // })
  const { searchData, updateRouteQuery } = useRouteQuery({
    search: {
      tab: '招聘看板',
      startDate: '',
      endDate: '',
      positionName: '',
      positionBid: '',
    },
  })

  const date = computed({
    get() {
      return [searchData.startDate || null, searchData.endDate || null]
    },
    set(value) {
      searchData.startDate = value?.[0] || ''
      searchData.endDate = value?.[1] || ''
    },
  })

  const positionNameSelectRef = useTemplateRef('positionNameSelectRef')
  watchEffect(() => {
    if (searchData.positionName && positionNameSelectRef.value) {
      positionNameSelectRef.value.getPositionNameOptions(searchData.positionName)
    }
  })
  // const route = useRoute()
  // watchEffect(() => {
  //   if (route.query?.tab) {
  //     activeTab.value = route.query.tab
  //   }
  //   if (route.query?.positionName) {
  //     searchData.positionName = route.query.positionName
  //   }
  //   if (route.query?.startDate && route.query?.endDate) {
  //     searchData.date = [route.query.startDate, route.query.endDate]
  //   }
  // })
</script>

<style lang="scss" scoped>
  .home {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 72px;
      padding: 0 20px;
      background-color: #fff;

      .picker {
        display: flex;
        align-items: center;
        gap: 12px;

        &_item {
          color: #909399;
          font-weight: 600;
          font-size: 18px;
          cursor: pointer;

          &:hover {
            color: #0055ff;
          }

          &.active {
            color: #0055ff;
          }
        }

        &_separator {
          color: #c0c4cc;
          font-size: 16px;
        }
      }
    }

    &_content {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
  }
</style>
