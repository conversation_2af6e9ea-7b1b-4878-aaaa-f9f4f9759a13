export const mapData = [
  {
    name: 'china',
    sex: [
      {
        name: '男',
        value: '19394',
      },
      {
        name: '女',
        value: '15310',
      },
    ],
    major1: [
      {
        name: '大专',
        children: [
          {
            name: '广播影视类',
            value: 10,

            // children: [
            //   {
            //     name: '录音技术与艺术',
            //     value: 100,
            //   },
            //   {
            //     name: '音像技术',
            //     value: 85,
            //   },
            //   {
            //     name: '影视编导',
            //     value: 96,
            //   },
            // ],
          },
          {
            name: '新闻出版类',
            value: 10,
            // children: [
            //   {
            //     name: '数字图文信息处理技术',
            //     value: 54,
            //   },
            //   {
            //     name: '出版策划与编辑',
            //     value: 74,
            //   },
            //   {
            //     name: '出版商务',
            //     value: 65,
            //   },
            // ],
          },

          {
            name: '餐饮类',
            value: 10,
            // children: [
            //   {
            //     name: '营养搭配',
            //     value: 60,
            //   },
            //   {
            //     name: '西式烹饪工艺',
            //     value: 45,
            //   },
            //   {
            //     name: '中式烹饪工艺',
            //     value: 45,
            //   },
            //   {
            //     name: '中西面点工艺',
            //     value: 30,
            //   },
            // ],
          },
          {
            name: '旅游类',
            value: 10,
            // children: [
            //   {
            //     name: '导游',
            //     value: 150,
            //   },
            //   {
            //     name: '旅游管理',
            //     value: 130,
            //   },
            // ],
          },
          {
            name: '公共卫生与卫生管理类',
            value: 10,
            // children: [
            //   {
            //     name: '公共卫生管理',
            //     value: 80,
            //   },
            //   {
            //     name: '卫生信息管理',
            //     value: 45,
            //   },
            // ],
          },
          {
            name: '康复治疗类',
            value: 10,

            // children: [
            //   {
            //     name: '康复治疗技术',
            //     value: 60,
            //   },
            //   {
            //     name: '康复辅助器具技术',
            //     value: 75,
            //   },
            // ],
          },
          {
            name: '通信类',
            value: 10,

            // children: [
            //   {
            //     name: '现代通信技术',
            //     value: 100,
            //   },
            //   {
            //     name: '现代移动通信技术',
            //     value: 85,
            //   },
            //   {
            //     name: '通信软件技术',
            //     value: 70,
            //   },
            //   {
            //     name: '卫星通信与导航技术',
            //     value: 23,
            //   },
            // ],
          },
          {
            name: '计算机类',
            value: 10,

            // children: [
            //   {
            //     name: '密码技术应用',
            //     value: 20,
            //   },
            //   {
            //     name: '动漫制作技术',
            //     value: 5,
            //   },
            // ],
          },
        ],
      },
      {
        name: '中专',
        children: [
          {
            name: '农业类',
            value: 10,

            // children: [
            //   {
            //     name: '种子生产技术',
            //     value: 100,
            //   },
            //   {
            //     name: '作物生产技术',
            //     value: 50,
            //   },
            //   {
            //     name: '循环农业与再生资源利用',
            //     value: 60,
            //   },
            //   {
            //     name: '家庭农场生产经营',
            //     value: 30,
            //   },
            // ],
          },
          {
            name: '林业类',
            value: 10,

            // children: [
            //   {
            //     name: '林业生产技术',
            //     value: 40,
            //   },
            //   {
            //     name: '园林技术',
            //     value: 30,
            //   },
            //   {
            //     name: '森林资源保护与管理',
            //     value: 35,
            //   },
            // ],
          },
          {
            name: '畜牧业类',
            value: 10,

            // children: [
            //   {
            //     name: '畜禽生产技术',
            //     value: 25,
            //   },
            //   {
            //     name: '特种动物养殖',
            //     value: 15,
            //   },
            //   {
            //     name: '宠物养护与经营',
            //     value: 50,
            //   },
            //   {
            //     name: '蚕桑生产与经营',
            //     value: 35,
            //   },
            // ],
          },
          {
            name: '渔业类',
            value: 10,

            // children: [
            //   {
            //     name: '淡水养殖',
            //     value: 24,
            //   },
            //   {
            //     name: '海水养殖',
            //     value: 23,
            //   },
            // ],
          },
          {
            name: '资源勘查类',
            value: 10,

            // children: [
            //   {
            //     name: '国土资源调查',
            //     value: 40,
            //   },
            //   {
            //     name: '地质调查与找矿',
            //     value: 38,
            //   },
            //   {
            //     name: '宝玉石加工与监测',
            //     value: 43,
            //   },
            // ],
          },
          {
            name: '地质类',
            value: 10,

            // children: [
            //   {
            //     name: '水文地质与工程地质勘查',
            //     value: 65,
            //   },
            //   {
            //     name: '钻探技术',
            //     value: 32,
            //   },
            //   {
            //     name: '掘进技术',
            //     value: 35,
            //   },
            // ],
          },
          {
            name: '测绘地理信息类',
            value: 10,

            // children: [
            //   {
            //     name: '工程测量技术',
            //     value: 80,
            //   },
            //   {
            //     name: '地图绘制与地理信息系统',
            //     value: 65,
            //   },
            //   {
            //     name: '地质与测量',
            //     value: 45,
            //   },
            //   {
            //     name: '航空摄影测量',
            //     value: 25,
            //   },
            // ],
          },
          {
            name: '石油与天然气类',
            value: 10,

            // children: [
            //   {
            //     name: '油气储运',
            //     value: 52,
            //   },
            //   {
            //     name: '石油地质录井与测井',
            //     value: 34,
            //   },
            //   {
            //     name: '石油钻井',
            //     value: 73,
            //   },
            //   {
            //     name: '油气开采',
            //     value: 36,
            //   },
            // ],
          },
          {
            name: '煤炭类',
            value: 10,

            // children: [
            //   {
            //     name: '采矿技术',
            //     value: 45,
            //   },
            //   {
            //     name: '矿山机电',
            //     value: 32,
            //   },
            // ],
          },
          {
            name: '金属与非金属矿类',
            value: 10,

            // children: [
            //   {
            //     name: '选矿技术',
            //     value: 10,
            //   },
            // ],
          },
          {
            name: '建筑设计类',
            value: 10,

            // children: [
            //   {
            //     name: '建筑表现',
            //     value: 100,
            //   },
            //   {
            //     name: '古建筑修缮',
            //     value: 75,
            //   },
            // ],
          },
          {
            name: '城乡规划与管理类',
            value: 10,

            // children: [
            //   {
            //     name: '城镇建设',
            //     value: 62,
            //   },
            // ],
          },
        ],
      },
    ],

    major2: [
      {
        name: '哲学类',
        children: [
          { name: '逻辑学', value: 10 },
          { name: '宗教学', value: 10 },
          { name: '伦理学', value: 10 },
        ],
      },
      {
        name: '经济学类',
        children: [
          { name: '经济学', value: 10 },
          { name: '经济统计学', value: 10 },
          { name: '国民经济管理', value: 10 },
          { name: '资源与环境经济学', value: 10 },
          { name: '商务经济学', value: 10 },
        ],
      },
      {
        name: '金融学类',
        children: [
          { name: '精算学', value: 10 },
          { name: '互联网金融', value: 10 },
          { name: '金融科技', value: 10 },
          { name: '金融审计', value: 10 },
        ],
      },
      {
        name: '经济与贸易类',
        children: [
          { name: '国际经济与贸易', value: 10 },
          { name: '贸易经济', value: 10 },
          { name: '国际经济发展合作', value: 10 },
        ],
      },
      {
        name: '公安技术类',
        children: [
          { name: '数据警务技术', value: 10 },
          { name: '食品药品环境犯罪侦查技术', value: 10 },
        ],
      },
      {
        name: '交叉工程类',
        children: [
          { name: '未来机器人', value: 10 },
          { name: '交叉工程', value: 10 },
        ],
      },
      {
        name: '植物生产类',
        children: [
          { name: '农学', value: 10 },
          { name: '园艺', value: 10 },
        ],
      },
      {
        name: '林学类',
        children: [
          { name: '林学', value: 10 },
          { name: '园林', value: 10 },
          { name: '森林保护', value: 10 },
          { name: '经济林', value: 10 },
          { name: '智慧林业', value: 10 },
        ],
      },
      {
        name: '水产类',
        children: [
          { name: '水产养殖学', value: 10 },
          { name: '海洋渔业科学与技术', value: 10 },
          { name: '水族科学与技术', value: 10 },
          { name: '水生动物医学', value: 10 },
        ],
      },
      {
        name: '草学类',
        children: [
          { name: '草业科学', value: 10 },
          { name: '草坪科学与工程', value: 10 },
        ],
      },
    ],
    major3: [
      {
        name: '哲学',
        children: [
          { name: '哲学', value: 10 },
          { name: '应用伦理', value: 10 },
        ],
      },
      {
        name: '经济学',
        children: [
          { name: '理论经济学', value: 10 },
          { name: '应用经济学', value: 10 },
          { name: '金融', value: 10 },
          { name: '应用统计', value: 10 },
          { name: '税务', value: 10 },
          { name: '国际商务', value: 10 },
          { name: '保险', value: 10 },
          { name: '资产评估', value: 10 },
          { name: '数字经济', value: 10 },
        ],
      },
      {
        name: '法学',
        children: [
          { name: '法学', value: 10 },
          { name: '政治学', value: 10 },
          { name: '社会学', value: 10 },
          { name: '民族学', value: 10 },
          { name: '马克思主义理论', value: 10 },
          { name: '公安学', value: 10 },
        ],
      },
      {
        name: '教育学',
        children: [
          { name: '教育', value: 10 },
          { name: '体育', value: 10 },
          { name: '国际中文教育', value: 10 },
          { name: '应用心理', value: 10 },
        ],
      },
      {
        name: '文学',
        children: [
          { name: '中国语言文学', value: 10 },
          { name: '外国语言文学', value: 10 },
          { name: '新闻传播学', value: 10 },
          { name: '翻译', value: 10 },
          { name: '新闻与传播', value: 10 },
        ],
      },
      {
        name: '历史学',
        children: [
          { name: '考古学', value: 10 },
          { name: '中国史', value: 10 },
          { name: '世界史', value: 10 },
          { name: '博物馆', value: 10 },
        ],
      },
      {
        name: '理学',
        children: [
          { name: '数学', value: 10 },
          { name: '物理学', value: 10 },
          { name: '化学', value: 10 },
        ],
      },
      {
        name: '工学',
        children: [
          { name: '核科学与技术', value: 10 },
          { name: '农业工程', value: 10 },
          { name: '林业工程', value: 10 },
          { name: '环境科学与工程', value: 10 },
          { name: '生物医学工程', value: 10 },
          { name: '食品科学与工程', value: 10 },
          { name: '城乡规划学', value: 10 },
          { name: '软件工程', value: 10 },
          { name: '生物工程', value: 10 },
          { name: '安全科学与工程', value: 10 },
          { name: '公安技术', value: 10 },
          { name: '网络空间安全', value: 10 },
          { name: '建筑', value: 10 },
          { name: '城乡规划', value: 10 },
          { name: '电子信息', value: 10 },
          { name: '机械', value: 10 },
          { name: '材料与化工', value: 10 },
          { name: '资源与环境', value: 10 },
          { name: '能源动力', value: 10 },
        ],
      },
    ],
    学历: [
      { name: '大专以下', value: '1331' },
      { name: '大专', value: '5619' },
      { name: '本科', value: '14193' },
      { name: '硕士', value: '8411' },
      { name: '博士', value: '5150' },
    ],
    学校分布: [
      { name: '中专院校', value: '1331' },
      { name: '大专院校', value: '5619' },
      { name: '普通三本院校', value: '5219' },
      { name: '普通二本院校', value: '6306' },
      { name: '普通一本院校', value: '5311' },
      { name: '211院校', value: '4389' },
      { name: '985院校', value: '3513' },
      { name: '双一流院校', value: '1987' },
      { name: '海外院校', value: '1029' },
    ],
    age: [
      {
        name: '男',
        data: [
          {
            name: '25岁以下',
            value: '9916',
          },
          {
            name: '26-35岁',
            value: '7897',
          },
          {
            name: '46-55岁',
            value: '1302',
          },
          {
            name: '56-65岁',
            value: '279',
          },
          {
            name: '65岁以上',
            value: '0',
          },
        ],
      },
      {
        name: '女',
        data: [
          {
            name: '25岁以下',
            value: '9294',
          },
          {
            name: '26-35岁',
            value: '5000',
          },
          {
            name: '46-55岁',
            value: '1016',
          },
          {
            name: '56-65岁',
            value: '0',
          },
          {
            name: '65岁以上',
            value: '0',
          },
        ],
      },
    ],
    word: [
      { name: '软件工程师', value: 25 },
      { name: '数据分析', value: 18 },
      { name: '平面设计', value: 22 },
      { name: '项目经理', value: 29 },
      { name: '会计师', value: 12 },
      { name: '人力资源', value: 21 },
      { name: '销售代表', value: 17 },
      { name: '财务分析', value: 26 },
      { name: '技术支持', value: 19 },
      { name: '运营管理', value: 28 },
      { name: '市场营销', value: 14 },
      { name: '土木工程', value: 23 },
      { name: '建筑师', value: 27 },
      { name: '电气工程', value: 15 },
      { name: '网络管理', value: 20 },
      { name: '机械工程', value: 16 },
      { name: '质量保证', value: 30 },
      { name: '工业工程', value: 11 },
      { name: '化学工程', value: 24 },
      { name: '药剂师', value: 13 },
      { name: '物理治疗', value: 22 },
      { name: '注册护士', value: 19 },
      { name: '职业治疗', value: 25 },
      { name: '语言病理', value: 18 },
      { name: '放射治疗', value: 21 },
      { name: '呼吸治疗', value: 30 },
      { name: '医学实验', value: 26 },
      { name: '牙科保健', value: 12 },
      { name: '图书管理', value: 28 },
      { name: '代课教师', value: 23 },
      { name: '警察', value: 14 },
      { name: '消防员', value: 15 },
    ],
    data: [
      {
        name: '湖北省',
        value: 4511,
        sex: [
          {
            name: '男',
            value: '2437',
          },
          {
            name: '女',
            value: '2074',
          },
        ],
        word: [
          { name: '软件工程师', value: 13 },
          { name: '数据分析师', value: 30 },
          { name: '市场营销', value: 12 },
          { name: '产品经理', value: 7 },
          { name: '财务分析师', value: 18 },
          { name: '人力资源', value: 29 },
          { name: '项目经理', value: 28 },
          { name: '销售代表', value: 19 },
          { name: '客服专员', value: 27 },
          { name: '物流经理', value: 10 },
          { name: '网页设计师', value: 5 },
          { name: '系统管理员', value: 21 },
          { name: '电气工程师', value: 30 },
          { name: '土木工程师', value: 6 },
          { name: '化学工程师', value: 10 },
          { name: '医生', value: 23 },
          { name: '护士', value: 8 },
          { name: '教师', value: 19 },
          { name: '律师', value: 20 },
          { name: '会计师', value: 24 },
          { name: '翻译', value: 22 },
          { name: '记者', value: 12 },
          { name: '编辑', value: 25 },
          { name: '建筑师', value: 27 },
          { name: '室内设计师', value: 29 },
          { name: '摄影师', value: 22 },
          { name: '平面设计师', value: 25 },
          { name: '机械工程师', value: 24 },
          { name: '生物学家', value: 13 },
          { name: '环境科学家', value: 9 },
        ],
        age: [
          {
            name: '男',
            data: [
              {
                name: '25岁以下',
                value: '1014',
              },
              {
                name: '26-35岁',
                value: '1242',
              },
              {
                name: '46-55岁',
                value: '118',
              },
              {
                name: '56-65岁',
                value: '63',
              },
              {
                name: '65岁以上',
                value: '0',
              },
            ],
          },
          {
            name: '女',
            data: [
              {
                name: '25岁以下',
                value: '1040',
              },
              {
                name: '26-35岁',
                value: '892',
              },
              {
                name: '46-55岁',
                value: '116',
              },
              {
                name: '56-65岁',
                value: '0',
              },
              {
                name: '65岁以上',
                value: '0',
              },
            ],
          },
        ],
        学历: [
          { name: '大专以下', value: '267' },
          { name: '大专', value: '319' },
          { name: '本科', value: '1747' },
          { name: '硕士', value: '1221' },
          { name: '博士', value: '957' },
        ],
        学校分布: [
          { name: '中专院校', value: '267' },
          { name: '大专院校', value: '319' },
          { name: '普通三本院校', value: '743' },
          { name: '普通二本院校', value: '983' },
          { name: '普通一本院校', value: '801' },
          { name: '211院校', value: '511' },
          { name: '985院校', value: '279' },
          { name: '双一流院校', value: '217' },
          { name: '海外院校', value: '391' },
        ],
        data: [
          {
            name: '武汉市',
            value: 1037,
            sex: [
              {
                name: '男',
                value: '558',
              },
              {
                name: '女',
                value: '479',
              },
            ],
            age: [
              {
                name: '男',
                data: [
                  {
                    name: '25岁以下',
                    value: '127',
                  },
                  {
                    name: '26-35岁',
                    value: '230',
                  },
                  {
                    name: '46-55岁',
                    value: '201',
                  },
                  {
                    name: '56-65岁',
                    value: '0',
                  },
                  {
                    name: '65岁以上',
                    value: '0',
                  },
                ],
              },
              {
                name: '女',
                data: [
                  {
                    name: '25岁以下',
                    value: '264',
                  },
                  {
                    name: '26-35岁',
                    value: '137',
                  },
                  {
                    name: '46-55岁',
                    value: '78',
                  },
                  {
                    name: '56-65岁',
                    value: '0',
                  },
                  {
                    name: '65岁以上',
                    value: '0',
                  },
                ],
              },
            ],
            word: [
              { name: '软件工程师', value: 21 },
              { name: '数据分析师', value: 11 },
              { name: '市场营销专员', value: 27 },
              { name: '产品经理', value: 29 },
              { name: '财务分析师', value: 18 },
              { name: '人力资源专员', value: 14 },
              { name: '项目经理', value: 15 },
              { name: '销售代表', value: 11 },
              { name: '客服专员', value: 29 },
              { name: '物流经理', value: 11 },
              { name: '网页设计师', value: 6 },
              { name: '系统管理员', value: 26 },
              { name: '电气工程师', value: 22 },
              { name: '土木工程师', value: 18 },
              { name: '化学工程师', value: 7 },
              { name: '医生', value: 14 },
              { name: '护士', value: 7 },
              { name: '教师', value: 25 },
              { name: '律师', value: 12 },
              { name: '建筑师', value: 19 },
              { name: '记者', value: 30 },
              { name: '编辑', value: 18 },
              { name: '翻译', value: 22 },
              { name: '心理咨询师', value: 16 },
              { name: '营养师', value: 28 },
              { name: '摄影师', value: 19 },
              { name: '化妆师', value: 27 },
              { name: '发型师', value: 9 },
              { name: '厨师', value: 7 },
              { name: '飞行员', value: 26 },
              { name: '空姐', value: 29 },
              { name: '船长', value: 13 },
              { name: '警察', value: 6 },
              { name: '消防员', value: 18 },
              { name: '药剂师', value: 10 },
            ],
            学历: [
              { name: '大专以下', value: '89' },
              { name: '大专', value: '102' },
              { name: '本科', value: '451' },
              { name: '硕士', value: '274' },
              { name: '博士', value: '112' },
            ],

            学校分布: [
              { name: '中专院校', value: '98' },
              { name: '大专院校', value: '102' },
              { name: '普通三本院校', value: '151' },
              { name: '普通二本院校', value: '141' },
              { name: '普通一本院校', value: '137' },
              { name: '211院校', value: '119' },
              { name: '985院校', value: '93' },
              { name: '双一流院校', value: '79' },
              { name: '海外院校', value: '117' },
            ],
          },
          { name: '襄阳市', value: 717 },
          { name: '荆州市', value: 690 },
          { name: '黄石市', value: 539 },
          { name: '宜昌市', value: 537 },
          { name: '十堰市', value: 421 },
          { name: '黄冈市', value: 291 },
          { name: '鄂州市', value: 279 },
        ],
      },
      { name: '江西省', value: 3823 },
      { name: '湖南省', value: 3197 },
      { name: '广东省', value: 2819 },
      { name: '山东省', value: 2425 },
      { name: '河南省', value: 2331 },
      { name: '四川省', value: 2018 },
      { name: '安徽省', value: 1845 },
      { name: '河北省', value: 1629 },
      { name: '山西省', value: 1418 },
      { name: '辽宁省', value: 1237 },
      { name: '福建省', value: 757 },
      { name: '上海市', value: 751 },
      { name: '江苏省', value: 673 },
      { name: '北京市', value: 643 },
      { name: '天津市', value: 549 },
      { name: '内蒙古自治区', value: 509 },
      { name: '浙江省', value: 507 },
      { name: '吉林省', value: 481 },
      { name: '黑龙江省', value: 439 },
      { name: '广西壮族自治区', value: 397 },
      { name: '贵州省', value: 359 },
      { name: '云南省', value: 325 },
      { name: '陕西省', value: 291 },
      { name: '甘肃省', value: 259 },
      { name: '青海省', value: 229 },
      { name: '宁夏回族自治区', value: 203 },
      { name: '新疆维吾尔自治区', value: 79 },
      { name: '西藏自治区', value: 0 },
      { name: '香港特别行政区', value: 0 },
      { name: '澳门特别行政区', value: 0 },
    ],
  },
]
