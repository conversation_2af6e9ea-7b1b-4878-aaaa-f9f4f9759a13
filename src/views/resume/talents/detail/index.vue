<template>
  <div class="page">
    <div class="backTitle">
      <BackTitle />
    </div>
    <div class="contentBox">
      <div class="talentsInfo">
        <div class="avatar">
          <img
            :src="baseInfo.avatarUrl"
            alt="" />
        </div>
        <div class="detailInfo">
          <div class="detailInfoHead">
            <div class="name">{{ baseInfo.name || '暂无姓名' }}</div>
            <span class="divider">/</span>
            <ElTag
              type="primary"
              :round="true"
              class="id">
              <i class="iconfont icon-ID"></i>
              {{ baseInfo.bid }}
            </ElTag>
          </div>
          <div class="education">
            <i class="iconfont icon-a-personalinformation infoIcon" />
            <div class="infoText">
              <div class="infoTextItem">{{ baseInfo.age }}岁</div>
              <div class="infoDivider" />
              <div class="infoTextItem">
                {{
                  {
                    '0': '大专以下',
                    '1': '大专',
                    '2': '本科',
                    '3': '硕士',
                    '4': '博士',
                  }[baseInfo.qualificationn]
                }}
              </div>
              <div class="infoDivider" />
              <div class="infoTextItem">
                {{ baseInfo.workTime || '暂无数据' }}
              </div>
              <div class="infoDivider" />
              <div class="infoTextItem">{{ baseInfo.phone }}</div>
              <div class="infoDivider" />
              <div class="infoTextItem">{{ baseInfo.email }}</div>
            </div>
          </div>
          <div
            class="education"
            style="margin-top: 8px">
            <i class="iconfont icon-positionFilled infoIcon" />
            <div class="infoText">
              <div class="infoTextItem">{{ baseInfo.position || '暂无数据' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="score">
        <div class="scoreText">人才综合评分</div>
        <div class="scoreNum">{{ score }}</div>
      </div>
    </div>
    <div class="detail">
      <ElTabs v-model="activeTab">
        <ElTabPane
          label="人才画像"
          name="人才画像">
          <DetailTab1 :data="detailTab1" />
        </ElTabPane>
        <ElTabPane
          label="人才简历"
          name="人才简历">
          <DetailTab2 :data="detailTab2" />
        </ElTabPane>
        <ElTabPane
          label="关联简历"
          name="关联简历">
          <DetailTab3 :data="detailTab3" />
        </ElTabPane>
      </ElTabs>
    </div>
  </div>
</template>

<script setup>
  import BackTitle from '@/components/back-title/index.vue'
  import DetailTab1 from './components/detail-tab1/index.vue'
  import DetailTab2 from './components/detail-tab2.vue'
  import DetailTab3 from './components/detail-tab3.vue'

  import * as apis from '@/apis/resume'
  import avatarFemale from '@/assets/images/avatar-female.png'
  import avatarMale from '@/assets/images/avatar-male.png'
  import avatarUnkonw from '@/assets/images/unkonw.png'
  import eventBus from '@/plugins/event-bus.js'

  const activeTab = ref('人才画像')
  const route = useRoute()
  const score = ref(0)
  onMounted(() => {
    onSearch()
  })
  const baseInfo = ref({})
  const detailTab1 = ref({})
  const detailTab2 = ref({})
  const detailTab3 = ref([])
  const getTalentDetails = async () => {
    try {
      const res = await apis.getTalentDetails({
        talentId: route.query.id,
      })
      const { avatarUrl, gender } = res.data.data

      baseInfo.value = {
        ...res.data.data,
        avatarUrl:
          avatarUrl && avatarUrl !== 'null'
            ? avatarUrl
            : gender == 0
              ? avatarMale
              : gender == 1
                ? avatarFemale
                : avatarUnkonw,
      }
    } catch (error) {
      console.log(error)
    }
  }
  const getTalentTags = async () => {
    try {
      const res = await apis.getTalentTags({ talentId: route.query.id })
      detailTab1.value.tags = res.data.data
    } catch (error) {
      console.log(error)
    }
  }
  const getTalentResumes = async () => {
    try {
      const res = await apis.getTalentResumes({
        talentId: route.query.id,
      })
      detailTab3.value = res.data.data
    } catch (error) {
      console.log(error)
    }
  }
  const getTalentNewest = async () => {
    try {
      const res = await apis.getTalentNewest({
        talentId: route.query.id,
      })
      detailTab2.value = res.data.data
    } catch (error) {
      console.log(error)
    }
  }
  eventBus.on('score', (val) => {
    score.value = val
  })
  const onSearch = () => {
    getTalentDetails()
    getTalentTags()
    getTalentResumes()
    getTalentNewest()
  }
</script>

<style lang="scss" scoped>
  .page {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;

    .backTitle {
      height: 68px;
      padding: 20px;
      border-radius: 4px;
      background: #fff;
    }

    .contentBox {
      position: relative;
      // height: 112px;
      margin-top: 16px;
      padding: 20px;
      border-radius: 4px;
      background: #fff;

      .talentsInfo {
        display: flex;

        .avatar {
          width: 64px;
          height: 64px;
        }

        .detailInfo {
          margin-left: 16px;

          .detailInfoHead {
            display: flex;
            align-items: center;
            margin-bottom: 14px;

            .name {
              color: #303133;
              font-weight: 500;
              font-size: 18px;
            }

            .divider {
              margin: 0 10px;
              color: #dcdfe6;
            }

            .id {
              font-size: 14px;
              & > i {
                margin-right: 8px;
                font-size: 14px;
              }
            }
          }

          .education {
            display: flex;
            align-items: center;
            color: #909399;
            font-size: 14px;

            .infoIcon {
              color: #cdd0d6;
            }

            .infoText {
              display: flex;
              align-items: center;
              margin-left: 12px;
              gap: 10px;

              .infoDivider {
                width: 1px;
                height: 13px;
                background: #dcdfe6;
              }
            }
          }
        }
      }

      .score {
        display: flex;
        position: absolute;
        top: 0;
        right: 0;
        align-items: center;
        width: 194px;
        height: 36px;
        padding-left: 40px;
        background: url('@/assets/images/talents-score-bg.png') no-repeat;

        .scoreText {
          margin-right: 10px;
          color: #a8abb2;
          font-size: 12px;
        }

        .scoreNum {
          color: #05f;
          font-weight: 600;
          font-size: 28px;
        }
      }
    }

    .detail {
      flex: 1;
      margin-top: 16px;
      padding: 20px;
      background: #fff;
    }
  }
</style>
