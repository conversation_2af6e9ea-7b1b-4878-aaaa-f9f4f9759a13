<template>
  <div
    ref="containerRef"
    class="detail_container">
    <div class="anchor">
      <ElAnchor :container="containerRef">
        <ElAnchorLink href="#baseInfo">基本信息</ElAnchorLink>
        <ElAnchorLink href="#contract">联系方式</ElAnchorLink>
        <ElAnchorLink href="#education">教育背景</ElAnchorLink>
        <ElAnchorLink href="#work">工作经历</ElAnchorLink>
        <ElAnchorLink href="#project">项目经历</ElAnchorLink>
        <ElAnchorLink href="#train">培训经历</ElAnchorLink>
        <ElAnchorLink href="#others">其他信息</ElAnchorLink>
      </ElAnchor>
    </div>
    <div class="title">
      <div class="deco" />
      <div
        id="baseInfo"
        class="titleText">
        基本信息
      </div>
    </div>
    <div class="infoBox">
      <div
        v-if="isNotEmpty(resumeBase.name)"
        class="infoItem">
        <span class="label">姓名</span>
        <span class="info">{{ resumeBase.name }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.gender)"
        class="infoItem">
        <span class="label">性别</span>
        <span class="info">{{ resumeBase.gender == '0' ? '男' : '女' }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.dateOfBirth)"
        class="infoItem">
        <span class="label">出生年月</span>
        <span class="info">{{ resumeBase.dateOfBirth }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.age)"
        class="infoItem">
        <span class="label">年龄</span>
        <span class="info">{{ resumeBase.age }}岁</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.birthplace)"
        class="infoItem">
        <span class="label">籍贯</span>
        <span class="info">{{ resumeBase.birthplace }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.currentLocation)"
        class="infoItem">
        <span class="label">现居住地</span>
        <span class="info">{{ resumeBase.currentLocation }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.registeredResidence)"
        class="infoItem">
        <span class="label">户口所在地</span>
        <span class="info">{{ resumeBase.registeredResidence }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.height)"
        class="infoItem">
        <span class="label">身高</span>
        <span class="info">{{ resumeBase.height }}cm</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.weight)"
        class="infoItem">
        <span class="label">体重</span>
        <span class="info">{{ resumeBase.weight }}kg</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.ethnic)"
        class="infoItem">
        <span class="label">民族</span>
        <span class="info">{{ resumeBase.ethnic }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.maritalStatus)"
        class="infoItem">
        <span class="label">婚姻状况</span>
        <span class="info">{{ resumeBase.maritalStatus }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.politicalStatus)"
        class="infoItem">
        <span class="label">政治面貌</span>
        <span class="info">{{ resumeBase.politicalStatus }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.degree)"
        class="infoItem">
        <span class="label">最高学历</span>
        <span class="info">
          {{
            {
              '0': '大专以下',
              '1': '大专',
              '2': '本科',
              '3': '硕士',
              '4': '博士',
            }[resumeBase.degree] || '本科'
          }}
        </span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.schoolName)"
        class="infoItem">
        <span class="label">学校名称</span>
        <span class="info">{{ resumeBase.schoolName }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.major)"
        class="infoItem">
        <span class="label">专业名称</span>
        <span class="info">{{ resumeBase.major }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.currentCompany)"
        class="infoItem">
        <span class="label">当前公司</span>
        <span class="info">{{ resumeBase.currentCompany }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.currentPosition)"
        class="infoItem">
        <span class="label">当前职位</span>
        <span class="info">{{ resumeBase.currentPosition }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.professionalTitle)"
        class="infoItem">
        <span class="label">职称级别</span>
        <span class="info">{{ resumeBase.professionalTitle }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.desiredPosition)"
        class="infoItem">
        <span class="label">应聘职位</span>
        <span class="info">{{ resumeBase.desiredPosition }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.expectedSalary)"
        class="infoItem">
        <span class="label">期望薪资</span>
        <span class="info">{{ resumeBase.expectedSalary }}元</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.expectLocation)"
        class="infoItem">
        <span class="label">期望工作地点</span>
        <span class="info">{{ resumeBase.expectLocation }}</span>
      </div>
    </div>
    <div
      v-if="
        isNotEmpty(resumeBase.phoneNumber) ||
        isNotEmpty(resumeBase.email) ||
        isNotEmpty(resumeBase.wechat) ||
        isNotEmpty(resumeBase.qq) ||
        isNotEmpty(resumeBase.homePhoneNumber)
      "
      class="title">
      <div class="deco" />
      <div
        id="contract"
        class="titleText">
        联系方式
      </div>
    </div>
    <div class="infoBox">
      <div
        v-if="isNotEmpty(resumeBase.phoneNumber)"
        class="infoItem">
        <span class="label">手机号</span>
        <span class="info">{{ resumeBase.phoneNumber }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.email)"
        class="infoItem">
        <span class="label">邮箱</span>
        <span class="info">{{ resumeBase.email }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.wechat)"
        class="infoItem">
        <span class="label">微信</span>
        <span class="info">{{ resumeBase.wechat }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.qq)"
        class="infoItem">
        <span class="label">QQ</span>
        <span class="info">{{ resumeBase.qq }}</span>
      </div>
      <div
        v-if="isNotEmpty(resumeBase.homePhoneNumber)"
        class="infoItem">
        <span class="label">座机号</span>
        <span class="info">{{ resumeBase.homePhoneNumber }}</span>
      </div>
    </div>
    <div
      v-if="isNotEmpty(resumeEduBack)"
      class="title">
      <div class="deco" />
      <div
        id="education"
        class="titleText">
        教育背景
      </div>
    </div>
    <ElTimeline
      v-if="resumeEduBack"
      class="timeline">
      <ElTimelineItem
        v-for="item in resumeEduBack"
        :key="item.id"
        :hollow="true"
        type="primary">
        <div style="width: 1400px">
          <div style="display: flex; align-items: center">
            <span class="timelineTitle">{{ item.schoolName }}</span>
            <span class="desc">{{ item.city }}</span>
            <div class="timelineInfo">
              <span
                v-if="item.major"
                class="timelineInfoItem">
                {{ item.major }}
              </span>
              <span
                v-if="item.degree"
                class="timelineInfoItem">
                {{
                  {
                    '0': '大专以下',
                    '1': '大专',
                    '2': '本科',
                    '3': '硕士',
                    '4': '博士',
                  }[item.degree] || '本科'
                }}
              </span>
              <span
                v-if="item.enrollmentMode"
                class="timelineInfoItem">
                {{ item.enrollmentMode }}
              </span>
              <span
                v-if="item.educationMode"
                class="timelineInfoItem">
                {{ item.educationMode }}
              </span>
            </div>
          </div>
          <div
            v-if="item.startDate && item.endDate"
            class="time">
            {{ item.startDate }}-{{ item.endDate }}
          </div>
          <div
            v-if="isNotEmpty(item.ranking) || isNotEmpty(item.gpa)"
            style="line-height: 40px">
            <span
              v-if="isNotEmpty(item.ranking)"
              class="label">
              专业排名
            </span>
            <span
              v-if="isNotEmpty(item.ranking)"
              class="info"
              style="margin-right: 380px">
              {{ item.ranking && `${item.ranking}%` }}
            </span>
            <span
              v-if="isNotEmpty(item.gpa)"
              class="label">
              GPA
            </span>
            <span
              v-if="isNotEmpty(item.gpa)"
              class="info">
              {{ item.gpa }}
            </span>
          </div>
          <div
            v-if="isNotEmpty(item.lessons)"
            style="line-height: 40px">
            <span class="label">主修课程</span>
            <span class="info">{{ item.lessons }}</span>
          </div>
          <div
            v-if="isNotEmpty(item.awards)"
            style="line-height: 40px">
            <span class="label">荣誉奖励</span>
            <span class="info">
              {{ item.awards }}
            </span>
          </div>
          <div
            v-if="isNotEmpty(item.researchAchievements)"
            style="display: flex; line-height: 40px">
            <span class="label">科研成果</span>
            <div
              class="info"
              style="flex: 1; line-height: 40px">
              {{ item.researchAchievements }}
            </div>
          </div>
        </div>
      </ElTimelineItem>
    </ElTimeline>
    <div
      v-if="isNotEmpty(resumeCampusExpers)"
      class="title">
      <div class="deco" />
      <div class="titleText">在校经历</div>
    </div>
    <ElTimeline
      v-if="resumeCampusExpers"
      class="timeline">
      <ElTimelineItem
        v-for="item in resumeCampusExpers"
        :key="item.id"
        :hollow="true"
        type="primary">
        <div style="width: 1400px">
          <div style="display: flex; align-items: center">
            <span class="timelineTitle">{{ item.organization }}</span>
            <div class="timelineInfo">
              <span class="timelineInfoItem">{{ item.jobName }}</span>
            </div>
          </div>
          <div class="time">2020.09-2021.02</div>
          <div style="display: flex; line-height: 40px">
            <span class="label">职务内容</span>
            <div
              class="info"
              style="flex: 1; line-height: 40px">
              {{ item.jobContent }}
            </div>
          </div>
        </div>
      </ElTimelineItem>
    </ElTimeline>
    <!-- 工作经历 -->
    <div
      v-if="resumeJobExpers"
      class="title">
      <div class="deco" />
      <div
        id="work"
        class="titleText">
        工作经历
      </div>
    </div>
    <ElTimeline
      v-if="resumeJobExpers"
      class="timeline">
      <ElTimelineItem
        v-for="item in resumeJobExpers"
        :key="item.id"
        :hollow="true"
        type="primary">
        <div style="width: 1400px">
          <div style="display: flex; align-items: center">
            <span
              v-if="item.companyName"
              class="timelineTitle">
              {{ item.companyName }}
            </span>
            <div class="timelineInfo">
              <span
                v-if="item.jobTitle"
                class="timelineInfoItem">
                {{ item.jobTitle }}
              </span>
              <span v-if="item.companyNature">
                {{ item.companyNature }}
              </span>
              <span
                v-if="item.jobType"
                class="timelineInfoItem">
                {{ item.jobType }}
              </span>
            </div>
          </div>
          <div
            v-if="item.startDate && item.endDate"
            class="time">
            {{ item.startDate }}-{{ item.endDate }}
          </div>
          <div style="line-height: 40px">
            <span class="label">工作地点</span>
            <span class="info">{{ item.workPlace || '暂无数据' }}</span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">工作内容</span>
            <span
              class="info"
              style="flex: 1">
              {{ item.workContent || '暂无数据' }}
            </span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">工作业绩</span>
            <div
              class="info"
              style="flex: 1">
              {{ item.workPerformance || '暂无数据' }}
            </div>
          </div>
        </div>
      </ElTimelineItem>
    </ElTimeline>
    <!-- 项目经历 -->
    <div class="title">
      <div class="deco" />
      <div
        id="project"
        class="titleText">
        项目经历
      </div>
    </div>
    <ElTimeline
      v-if="resumeProjectExpers"
      class="timeline">
      <ElTimelineItem
        v-for="item in resumeProjectExpers"
        :key="item.id"
        :hollow="true"
        type="primary">
        <div style="width: 1400px">
          <div style="display: flex; align-items: center">
            <span class="timelineTitle">{{ item.projectName }}</span>
            <div class="timelineInfo">
              <span
                v-if="item.projectRole"
                class="timelineInfoItem">
                {{ item.projectRole }}
              </span>
              <span
                v-if="item.companyName"
                class="timelineInfoItem">
                {{ item.companyName }}
              </span>
            </div>
          </div>
          <div
            v-if="item.startDate && item.endDate"
            class="time">
            {{ item.startDate }}-{{ item.endDate }}
          </div>
          <div style="line-height: 40px">
            <span class="label">项目干系人</span>
            <span class="info">
              {{ item.projectStakeholders || '暂无数据' }}
            </span>
          </div>
          <div style="line-height: 40px">
            <span class="label">团队规模</span>
            <span class="info">{{ item.teamSize }}人</span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">项目介绍</span>
            <span
              class="info"
              style="flex: 1">
              {{ item.projectContent || '暂无数据' }}
            </span>
          </div>
          <div style="line-height: 40px">
            <span class="label">项目职责</span>
            <span class="info">
              {{ item.projectResponsibilities || '暂无数据' }}
            </span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">项目收益</span>
            <div
              class="info"
              style="flex: 1">
              {{ item.benefits || '暂无数据' }}
            </div>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">主要贡献</span>
            <div
              class="info"
              style="flex: 1">
              {{ item.contributions || '暂无数据' }}
            </div>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">科研成果</span>
            <div
              class="info"
              style="flex: 1">
              {{ item.researchAchievements || '暂无数据' }}
            </div>
          </div>
        </div>
      </ElTimelineItem>
    </ElTimeline>
    <!-- 培训经历 -->
    <div class="title">
      <div class="deco" />
      <div
        id="train"
        class="titleText">
        培训经历
      </div>
    </div>
    <ElTimeline
      v-if="resumeTrainExpers"
      class="timeline">
      <ElTimelineItem
        v-for="item in resumeTrainExpers"
        :key="item.id"
        :hollow="true"
        type="primary">
        <div style="width: 1400px">
          <div style="display: flex; align-items: center">
            <span class="timelineTitle">{{ item.trainingProject }}</span>
            <div class="timelineInfo">
              <span class="timelineInfoItem">
                {{ item.trainingInstitutions }}
              </span>
              <span
                v-if="item.trainingPlace"
                class="timelineInfoItem">
                {{ item.trainingPlace }}
              </span>
            </div>
          </div>
          <div
            v-if="item.startDate && item.endDate"
            class="time">
            {{ item.startDate }}-{{ item.endDate }}
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">培训内容</span>
            <span
              class="info"
              style="flex: 1">
              {{ item.trainingContent }}
            </span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">培训课程</span>
            <span
              class="info"
              style="flex: 1">
              {{ item.trainingLessons }}
            </span>
          </div>
          <div style="display: flex; line-height: 40px">
            <span class="label">培训成果</span>
            <div
              class="info"
              style="flex: 1">
              {{ item.trainingAchievements }}
            </div>
          </div>
        </div>
      </ElTimelineItem>
    </ElTimeline>
    <!-- 其他信息 -->
    <div class="title">
      <div class="deco" />
      <div
        id="others"
        class="titleText">
        其他信息
      </div>
    </div>
    <div
      v-if="resumeOtherInfo"
      class="timeline"
      style="width: 1400px">
      <div
        v-if="isNotEmpty(resumeOtherInfo?.lessons)"
        style="display: flex; line-height: 40px">
        <span class="label">主修课程</span>
        <span
          class="info"
          style="flex: 1">
          {{ resumeOtherInfo?.lessons }}
        </span>
      </div>
      <div style="display: flex; line-height: 40px">
        <span class="label">专业技能</span>
        <div
          class="info"
          style="flex: 1">
          <div
            v-if="isNotEmpty(resumeOtherInfo?.skillsMap?.入门)"
            class="flex gap-[5px]">
            入门：
            <span>{{ resumeOtherInfo?.skillsMap?.入门 }}</span>
          </div>
          <div
            v-if="isNotEmpty(resumeOtherInfo?.skillsMap?.熟悉)"
            class="flex gap-[5px]">
            熟悉：
            <span>{{ resumeOtherInfo?.skillsMap?.熟悉 }}</span>
          </div>
          <div
            v-if="isNotEmpty(resumeOtherInfo?.skillsMap?.精通)"
            class="flex gap-[5px]">
            精通：
            <span>{{ resumeOtherInfo?.skillsMap?.精通 }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="isNotEmpty(resumeOtherInfo?.certificates)"
        style="display: flex; line-height: 40px">
        <span class="label">资格证书</span>
        <div
          class="info"
          style="flex: 1">
          {{ resumeOtherInfo?.certificates }}
        </div>
      </div>
      <div
        v-if="isNotEmpty(resumeOtherInfo?.languages)"
        style="display: flex; line-height: 40px">
        <span class="label">语言能力</span>
        <div
          class="info"
          style="flex: 1">
          {{ resumeOtherInfo?.languages }}
        </div>
      </div>
      <div
        v-if="isNotEmpty(resumeOtherInfo?.awards)"
        style="display: flex; line-height: 40px">
        <span class="label">荣誉奖励</span>
        <div
          class="info"
          style="display: flex; flex: 1; gap: 10px">
          <div
            class="info"
            style="flex: 1">
            {{ resumeOtherInfo?.awards }}
          </div>
        </div>
      </div>
      <div
        v-if="isNotEmpty(resumeOtherInfo?.interests)"
        style="display: flex; line-height: 40px">
        <span class="label">兴趣爱好</span>
        <div
          class="info"
          style="flex: 1">
          {{ resumeOtherInfo?.interests || '暂无数据' }}
        </div>
      </div>
      <div
        v-if="isNotEmpty(resumeOtherInfo?.selfEvaluation)"
        style="display: flex; line-height: 40px">
        <span class="label">自我评价</span>
        <div
          class="info"
          style="flex: 1">
          {{ resumeOtherInfo?.selfEvaluation || '暂无数据' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { isNotEmpty } from '@/utils/index'

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
  })
  const { data } = toRefs(props)

  // 基本信息
  const resumeBase = computed(() => {
    return data.value.resumeBase || {}
  })

  // 	教育背景
  const resumeEduBack = computed(() => {
    return data.value.resumeEduBack
    // if (Array.isArray(resumeEduBack)) {
    //   return resumeEduBack.filter((item) => {
    //     delete item.id
    //     delete item.resumeBid
    //     return isNotEmpty(item)
    //   })
    // } else {
    //   return []
    // }
  })
  // 	在校经历
  const resumeCampusExpers = computed(() => {
    return data.value.resumeCampusExpers
  })
  // 	工作经历
  const resumeJobExpers = computed(() => {
    return data.value.resumeJobExpers
  })
  // 	项目经历
  const resumeProjectExpers = computed(() => {
    return data.value.resumeProjectExpers
  })
  // 	培训经历
  const resumeTrainExpers = computed(() => {
    return data.value.resumeTrainExpers
  })
  // 其他信息
  const resumeOtherInfo = computed(() => {
    return data.value.resumeOtherInfo
  })
  const containerRef = ref()
</script>

<style lang="scss" scoped>
  .detail_container {
    position: relative;
    height: 604px;
    overflow: auto;

    .anchor {
      z-index: 9999;
      position: sticky;
      top: 0;
      right: 28px;
      float: right;
    }

    .title {
      display: flex;
      align-items: center;
      margin: 20px 0;
      background: #fff;

      .deco {
        width: 4px;
        height: 16px;
        margin-right: 12px;
        border-radius: 2px;
        background: #05f;
      }
    }

    .timeline {
      padding-left: 2px;
      font-size: 14px;

      .label {
        margin-right: 16px;
        color: #303133;
        font-size: 14px;
      }

      .info {
        color: #606266;
        font-size: 14px;
      }

      .timelineTitle {
        margin-right: 8px;
        color: #303133;
        font-weight: 500;
        font-size: 16px;
      }

      .desc {
        color: #909399;
      }

      .timelineInfo {
        margin-left: 54px;

        .timelineInfoItem {
          padding: 0 10px;
          border-right: 1px solid #dcdfe6;
          color: #303133;

          &:last-child {
            border: 0;
          }
        }
      }

      .time {
        margin: 8px 0;
        color: #909399;
      }
    }

    .infoBox {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 1000px;

      .infoItem {
        display: flex;
        align-items: center;
        width: 50%;
        height: 40px;

        .label {
          margin-right: 16px;
          color: #303133;
          font-size: 14px;
        }

        .info {
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
</style>
