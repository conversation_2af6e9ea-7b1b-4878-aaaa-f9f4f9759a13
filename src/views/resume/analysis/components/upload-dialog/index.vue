<template>
  <ElDialog
    v-model="visible"
    title="新增任务"
    width="500"
    :destroy-on-close="true">
    <ElForm
      ref="formRef"
      :rules="rulesData"
      :model="formData"
      label-position="top">
      <ElFormItem
        label="职位名称"
        prop="positionBid">
        <JobPicker v-model="formData.positionBid" />
      </ElFormItem>
      <ElFormItem
        label="来源渠道"
        prop="sourceChannel">
        <ElSelect v-model="formData.sourceChannel">
          <ElOption
            v-for="item in RESUME_SOURCE_CHANNEL"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        label="上传文件"
        prop="fileList">
        <FileUpload
          v-model:file-list="formData.fileList"
          :max-size="50" />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton
        :loading="confirmLoading"
        type="primary"
        @click="handleConfirm">
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import * as apis from '@/apis/resume'
  import JobPicker from './job-picker.vue'
  import FileUpload from './file-upload.vue'
  import { useDict } from '@/stores/dict'

  const { RESUME_SOURCE_CHANNEL } = useDict('RESUME_SOURCE_CHANNEL')

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  const emit = defineEmits(['on-success'])

  const formData = reactive({
    positionBid: '',
    sourceChannel: '手动上传',
    fileList: [],
  })
  const rulesData = reactive({
    positionBid: [{ required: true, message: '请选择职位名称', trigger: ['blur', 'change'] }],
    sourceChannel: [{ required: true, message: '请选择来源渠道', trigger: ['blur', 'change'] }],
    fileList: [{ required: true, message: '请上传文件', trigger: ['blur', 'change'] }],
  })

  const confirmLoading = ref(false)

  const formRef = useTemplateRef('formRef')

  function handleCancel() {
    formRef.value.resetFields()
    visible.value = false
  }

  function handleConfirm() {
    formRef.value.validate(async (valid) => {
      if (valid) {
        confirmLoading.value = true
        const file = formData.fileList[0]
        const fileName = file.name.split('/').pop()
        const resumeUrl = file.response.data
        try {
          await apis.uploadTaskAnalysisResume({
            fileName,
            resumeUrl,
            positionBid: formData.positionBid,
            sourceChannel: formData.sourceChannel,
          })
          ElMessage.success('文件上传成功')

          handleCancel()

          emit('on-success')
        } catch (error) {
          ElMessage.error(error.message)
        } finally {
          confirmLoading.value = false
        }
      } else {
        console.log('error submit!!')
        return false
      }
    })
  }
</script>

<style lang="scss" scoped></style>
