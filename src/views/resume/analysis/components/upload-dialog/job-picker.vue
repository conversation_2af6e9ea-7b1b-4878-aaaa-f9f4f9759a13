<template>
  <div class="w-full">
    <ElInput
      :value="inputValue"
      placeholder="请选择职位名称"
      @click="handleEdit" />

    <PublishedJobDialog
      v-model:visible="dialogVisible"
      v-model="selectedRow"
      @confirm="(row) => (dialogVisible = false)" />
  </div>
</template>

<script setup>
  import { PublishedJobDialog } from '@/components'

  const model = defineModel({
    type: String,
    default: '',
  })

  const inputValue = computed(() => {
    return selectedRow.value?.positionName || ''
  })

  const dialogVisible = ref(false)
  function handleEdit() {
    dialogVisible.value = true
  }

  const selectedRow = ref({})
  watch(
    () => selectedRow.value,
    (nv) => {
      model.value = nv.bid
    },
    {
      deep: true,
    },
  )
</script>

<style lang="scss" scoped></style>
