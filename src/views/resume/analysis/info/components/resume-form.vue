<template>
  <div class="resume_original">
    <ElTabs type="border-card">
      <ElTabPane label="简历信息">
        <ResumeInfo @on-success="onSuccess" />
      </ElTabPane>
      <ElTabPane label="简历标签">
        <ResumeTag ref="resumeTagRef" />
      </ElTabPane>
    </ElTabs>
  </div>
</template>

<script setup>
  import ResumeInfo from './resume-info.vue'
  import ResumeTag from './resume-tag.vue'

  const resumeTagRef = useTemplateRef('resumeTagRef')
  const onSuccess = () => {
    resumeTagRef.value.getResumeInfoTagNums()
    resumeTagRef.value.getResumeInfoTags()
    resumeTagRef.value.getResumeInfoScore()
  }
</script>

<style lang="scss" scoped>
  .resume_original {
    flex: 1;
    height: 100%;

    &:deep(.el-tabs) {
      .is-active {
        color: #000;
      }

      .el-tabs__item:hover {
        color: #000 !important;
      }
    }
  }
</style>
