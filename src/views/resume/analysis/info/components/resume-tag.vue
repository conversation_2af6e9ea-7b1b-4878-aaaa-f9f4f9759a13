<template>
  <div class="resume_tag">
    <header>
      <!-- echarts -->
      <div class="echart">
        <VChart
          class="h-[108px] w-[108px]"
          :option="option"
          :autoresize="true" />
        <div class="echart_footer">简历综合评分</div>
      </div>
      <!-- 数据 -->
      <div class="data">
        <div
          v-for="item in tags"
          :key="item.name"
          class="data_item">
          <p class="data_item_title">{{ item.name }}</p>
          <span
            class="data_item_num"
            :style="{ color: item.color }">
            {{ item.count }}
          </span>
        </div>
        <!-- <div class="data_item">
          <p class="data_item_title">其它标签数</p>
          <span class="data_item_num text-[#13C2C2]">12</span>
        </div>
        <div class="data_item">
          <p class="data_item_title">风险点标签数</p>
          <span class="data_item_num text-[#E6A23C]">12</span>
        </div>
        <div class="data_item">
          <p class="data_item_title">问题点标签数</p>
          <span class="data_item_num text-[#F56C6C]">12</span>
        </div> -->
      </div>
    </header>
    <main>
      <TagType
        v-for="(item, index) in tagList"
        :key="index"
        :title="item.name"
        :sub-list="item.tags ??= []"
        :tag-list="item.directlyTags ??= []"
        :tag-type="item.color" />
      <!-- <TagType
        title="其他标签"
        :tag-list="['1', '2', '3']"
        tag-type="success" />
      <TagType
        title="风险点标签"
        :tag-list="['较长空白期']"
        tag-type="success" />
      <TagType
        title="问题点标签"
        :tag-list="['培训经历与工作经历不符']"
        tag-type="success" /> -->
    </main>
  </div>
</template>

<script setup>
  import * as apis from '@/apis/resume'
  import TagType from './tag-type.vue'

  const route = useRoute()
  const tags = ref([])
  let seriesData = ref(100) //环形图的具体  百分比
  let total = 100
  const isFloat = (val) => {
    return val % 1 !== 0
  }
  const option = computed(() => ({
    tooltip: {
      trigger: 'item',
    },
    title: {
      show: true,
      text: seriesData.value,
      itemGap: 100,
      left: '45%',
      top: '30%',
      textStyle: {
        fontSize: isFloat(seriesData.value) ? 28 : 38,
        lineHeight: 44,
        color: '#3B6BFA',
      },
      textAlign: 'center',
    },
    series: [
      {
        type: 'pie',
        radius: ['80%', '100%'],
        center: ['50%', '50%'],
        startAngle: -140,
        endAngle: 250,
        data: [
          {
            value: seriesData.value,
            itemStyle: {
              color: {
                type: 'linear',
                colorStops: [
                  {
                    offset: 0,
                    color: '#0055FF',
                  },
                  {
                    offset: 0.8,
                    color: '#40A9FF',
                  },
                ],
              },
            },
            tooltip: {
              show: false,
            },
            emphasis: {
              disabled: false,
              label: {
                show: false,
              },
              scale: false,
              itemStyle: {
                color: 'inherit',
              },
            },
            labelLine: {
              show: false,
            },
          },
          {
            value: total - seriesData.value,
            name: '',
            emphasis: {
              disabled: true,
              label: {
                show: false,
              },
            },
            itemStyle: {
              color: 'rgba(0, 224, 255, .2)',
            },
            labelLine: {
              show: false,
            },
            tooltip: {
              show: false,
            },
          },
          {
            value: (total / 0.8) * 0.2,
            itemStyle: {
              color: 'none',
              decal: {
                symbol: 'none',
              },
            },
            labelLine: {
              show: false,
            },
            label: {
              show: false,
            },
          },
        ],
      },
    ],
  }))
  const tagList = ref([])
  const getResumeInfoTagNums = async () => {
    try {
      const res = await apis.getResumeInfoTagNums({
        resumeId: route.query.resumeBid,
      })
      tags.value = res.data.data.map((item) => {
        return {
          ...item,
          color:
            {
              亮点标签数: '#409EFF',
              其它标签数: '#13C2C2',
              风险点标签数: '#E6A23C',
              问题点标签数: '#F56C6C',
            }[item.name] || '#409EFF',
        }
      })
    } catch (error) {
      console.log(error)
    }
  }
  const getResumeInfoTags = async () => {
    try {
      const res = await apis.getResumeInfoTags({
        resumeId: route.query.resumeBid,
      })
      tagList.value = res.data.data.map((item) => {
        return {
          ...item,
          color:
            {
              亮点标签: 'primary',
              其他标签: 'success',
              风险点标签: 'warning',
              问题点标签: 'danger',
            }[item.name] || 'primary',
        }
      })
    } catch (error) {
      console.log(error)
    }
  }
  const getResumeInfoScore = async () => {
    try {
      const res = await apis.getResumeInfoScore({
        resumeId: route.query.resumeBid,
      })
      seriesData.value = res.data.data.score
    } catch (error) {
      console.log(error)
    }
  }
  onMounted(() => {
    getResumeInfoScore()
    getResumeInfoTagNums()
    getResumeInfoTags()
  })

  defineExpose({
    getResumeInfoTagNums,
    getResumeInfoTags,
    getResumeInfoScore,
  })
</script>

<style lang="scss" scoped>
  .resume_tag {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 256px);
    overflow-y: auto;

    header {
      position: relative;
      height: 144px;
      padding-top: 38px;

      .echart {
        display: flex;
        position: absolute;
        top: 0;
        left: 20px;
        flex-direction: column;
        justify-content: space-between;
        width: 130px;
        height: 130px;
        padding: 11px;
        background-color: var(--white);

        &_footer {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 30px;
          border-radius: 30px;
          background: linear-gradient(90deg, #05f 0%, #00a4ff 100%);
          color: var(--white);
          line-height: 30px;
          text-align: center;
        }

        &_pie {
          flex: 1;
        }
      }

      .data {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 100%;
        padding: 17px 20px;
        padding-left: 170px;
        background-color: #fafafa;

        &_item {
          position: relative;
          flex: 1;
          height: 100%;
          padding: 10px 20px;

          &:not(:first-of-type) {
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 1px;
              height: calc(100% - 20px);
              transform: translateY(-50%);
              background-color: rgb(0 0 0 / 6%);
              content: '';
            }
          }

          &_title {
            color: #606266;
            font-size: 12px;
            line-height: 20px;
          }

          &_num {
            font-weight: 500;
            font-size: 20px;
          }
        }
      }
    }

    main {
      flex: 1;
      margin-top: 30px;
    }
  }
</style>
