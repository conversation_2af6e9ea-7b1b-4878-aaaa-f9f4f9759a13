<template>
  <TablePageLayout title="企业微信账号">
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="用户名"
          prop="username">
          <ElInput
            v-model="searchData.username"
            style="width: 340px"
            placeholder="请输入用户名" />
        </ElFormItem>
        <ElFormItem
          label="手机号"
          prop="phone">
          <ElInput
            v-model="searchData.phone"
            style="width: 340px"
            placeholder="请输入手机号" />
        </ElFormItem>
        <ElFormItem
          label="所属账号"
          prop="createdByName">
          <KeywordSearchInput
            v-model="searchData.createdByName"
            url=""
            style="width: 340px" />
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="tableLoading"
        :max-height="maxHeight"
        :data="tableData"
        :border="true">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无人才微信账号" />
        </template>
        <ElTableColumn
          label="企业名称"
          prop="企业名称" />
        <ElTableColumn
          label="用户名"
          prop="username" />
        <ElTableColumn
          label="手机号"
          prop="phone" />
        <ElTableColumn
          label="状态"
          prop="status">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  待验证: 'warning',
                  满员: 'danger',
                  正常: 'success',
                }[row.status]
              ">
              {{ row.status }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="登录状态"
          prop="登录状态">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  未登录: 'info',
                  已登录: 'success',
                }[row.status]
              ">
              {{ row.status }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="绑定人才总数"
          prop="绑定人才总数" />
        <ElTableColumn
          label="今日绑定数"
          prop="今日绑定数" />
        <ElTableColumn
          label="所属账号"
          prop="createdByName" />
        <ElTableColumn label="操作">
          <template #default="{ row }">
            <ElButton
              type="primary"
              link>
              验证
            </ElButton>
            <ElButton
              type="primary"
              link>
              扫码登录
            </ElButton>
            <ElButton
              type="primary"
              link
              @click="
                router.push({ path: '/account/enterprise-wechat/detail', query: { bid: row.bid } })
              ">
              详情
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageOrSizeChange(true)"
        @current-change="handlePageOrSizeChange(false)" />
    </template>
  </TablePageLayout>

  <LoginWechat
    v-model="dialogVisible"
    :loading="dialogLoading"
    @over="filled_over"></LoginWechat>
</template>

<script setup>
  import { getWeChatWorkList } from '@/apis/wechat'
  import { KeywordSearchInput, TablePageLayout } from '@/components'
  import { ElButton } from 'element-plus'
  import LoginWechat from '../components/login-wechat.vue'
  import { useLoginDialog } from '../hooks'
  const router = useRouter()
  const searchData = reactive({
    username: '',
    phone: '',
    createdByName: '',
  })

  // 查询
  function handleSearch() {
    paginationData.pageNum = 1
    getTableData()
  }

  const searchFormRef = useTemplateRef('searchFormRef')
  // 重置
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  const tableData = ref([])
  const tableLoading = ref(false)
  function getTableData() {
    tableLoading.value = true
    getWeChatWorkList({
      ...searchData,
      pageSize: paginationData.pageSize,
      pageNum: paginationData.pageNum,
    })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page
      })
      .finally(() => {
        tableLoading.value = false
      })
  }

  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
  // 改变每页条数
  function handlePageOrSizeChange(isResize) {
    if (isResize) paginationData.pageNum = 1
    handleSearch()
  }

  const { dialogVisible, dialogLoading, filled_over } = useLoginDialog()
  onMounted(() => {
    getTableData()
  })
</script>

<style lang="scss" scoped></style>
