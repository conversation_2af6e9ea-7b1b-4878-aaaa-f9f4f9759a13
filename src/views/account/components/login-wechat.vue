<template>
  <ElDialog
    v-model="model"
    width="600"
    center
    :style="{ padding: '0' }"
    @open="dialogOptions.open">
    <div
      v-if="dialogOptions.isQrCode"
      v-loading="loading"
      class="m-auto flex w-[200px] flex-col gap-[24px]">
      <div class="title flex w-full items-center justify-center gap-[15px]">
        <img
          class="h-[28px] w-[28px]"
          src="@/assets/images/wechat/logo.png"
          alt="" />
        <span class="text-[20px] font-bold text-[#0082EF]">企业微信登录</span>
      </div>
      <div class="mb-[50px] flex justify-center">
        <ElImage
          style="width: 176px; height: 176px"
          :src="dialogOptions.QRcode"
          alt=""
          :preview-src-list="[]" />
      </div>
      <ElButton @click="dialogOptions.isQrCode = false">click</ElButton>
    </div>
    <div
      v-else
      v-loading="loading">
      <div class="title flex w-full flex-col items-center justify-center gap-[16px] pb-[70px]">
        <span class="text-[20px] font-bold">新设备首次登录验证</span>
        <span>请及时输入验证码登录</span>
        <div class="input_list flex gap-[12px]">
          <ElInput
            v-for="(item, index) in dialogOptions.inputCode"
            :key="item.label"
            ref="inputRef"
            v-model="dialogOptions.inputCode[index].value"
            :maxlength="1"
            style="width: 40px"
            @input="(val) => dialogOptions.checkNext(val, index)"
            @clear="(val) => dialogOptions.checkNext(val, index)" />
        </div>
      </div>
    </div>
    <template
      v-if="dialogOptions.isQrCode"
      #footer>
      <div class="flex h-[40px] items-center justify-center gap-[10px] bg-[#ECF5FF]">
        <i class="iconfont icon-warning-filled text-[14px] text-[#409EFF]"></i>
        <span class="text-[#0082EF]">请使用手机企业微信扫码登录</span>
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
  const model = defineModel({
    type: Boolean,
    default: false,
  })
  const emits = defineEmits(['over'])
  const { loading } = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
  })
  const dialogOptions = reactive({
    visible: true,
    open: () => {},
    isQrCode: true,
    QRcode: '',
    inputRef: useTemplateRef('inputRef'),
    inputCode: Array.from({ length: 6 }).map((i, index) => ({ label: `_${index}`, value: '' })),
    checkNext: (val, index) => {
      // 删除当前输入框的值 需要聚焦至上一个输入框
      if (val === '') {
        // 判断上一个输入框是否有对应的实例
        if (!dialogOptions.inputRef[index - 1]) return
        dialogOptions.inputRef[index - 1].focus()
        dialogOptions.inputRef[index - 1].select()
        return
      }
      // 判断下一个输入框是否有值，如果没值则聚焦
      if (index !== 5 && !dialogOptions.inputCode[index + 1].value) {
        dialogOptions.inputRef[index + 1].focus()
      }
      // 首先判断是否全部填写完毕
      if (dialogOptions.inputCode.every((item) => item.value)) {
        const str = dialogOptions.inputCode.map((item) => item.value).join('')
        console.log('全部填写完毕', str)
        emits('over', str)
      }
    },
  })
</script>

<style lang="scss" scoped>
  :deep(.input_list) {
    .el-input__inner {
      text-align: center;
    }
  }
</style>
