<template>
  <TablePageLayout title="人才微信账号">
    <!-- <template #header-extra>
      <ElButton
        class="align-center flex"
        :link="true"
        type="primary"
        @click="dialogOptions.visible = true">
        <i class="iconfont icon-upload text-[12px]!" />
        <span class="ml-[4px]">新增微信账号</span>
      </ElButton>
    </template> -->
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="人才ID"
          prop="talentBid">
          <ElInput
            v-model="searchData.talentBid"
            style="width: 340px"
            placeholder="请输入邮箱名称" />
        </ElFormItem>
        <ElFormItem
          label="人才姓名"
          prop="name">
          <ElInput
            v-model="searchData.name"
            style="width: 340px"
            placeholder="请输入人才姓名" />
        </ElFormItem>
        <ElFormItem
          label="状态"
          prop="status">
          <ElSelect
            v-model="searchData.status"
            style="width: 340px">
            <ElOption
              v-for="item in TALENT_WECHAT_STATUS"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="认领企微"
          prop="claimWxwork">
          <KeywordSearchInput
            v-model="searchData.claimWxwork"
            url=""
            style="width: 340px" />
        </ElFormItem>
        <ElFormItem
          label="所属账号"
          prop="belongAccount">
          <KeywordSearchInput
            v-model="searchData.belongAccount"
            url=""
            style="width: 340px" />
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="tableLoading"
        :max-height="maxHeight"
        :data="tableData"
        :border="true">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无人才微信账号" />
        </template>
        <ElTableColumn
          label="人才ID"
          prop="talentBid" />
        <ElTableColumn
          label="姓名"
          prop="name" />
        <ElTableColumn
          label="手机号"
          prop="phone" />
        <ElTableColumn
          label="微信号"
          prop="wechat" />
        <ElTableColumn
          label="状态"
          prop="status">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  0: 'warning',
                  1: 'primary',
                  2: 'danger',
                  3: 'success',
                  4: 'info',
                }[row.status]
              ">
              {{
                {
                  0: '待绑定',
                  1: '待通过',
                  2: '绑定失败',
                  3: '绑定成功',
                  4: '被单删',
                }[row.status]
              }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="备注"
          prop="备注"></ElTableColumn>
        <ElTableColumn
          label="所属账号"
          prop="belongAccount" />
        <ElTableColumn
          label="认领企微"
          prop="claimWxwork" />
        <ElTableColumn
          label="状态更新时间"
          prop="状态更新时间" />
        <ElTableColumn label="操作">
          <template #default="{ row }">
            <ElButton
              type="primary"
              :disabled="!(row.status === '绑定失败' && row['备注'] === '账号有误')"
              link
              @click="dialogOptions.edit(row)">
              编辑
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageOrSizeChange(true)"
        @current-change="handlePageOrSizeChange(false)" />
    </template>
  </TablePageLayout>
  <ElDialog
    v-model="dialogOptions.visible"
    width="480">
    <template #header>
      <h2 class="font-bold">修改微信号</h2>
    </template>
    <ElFormItem label="微信账号">
      <ElInput
        v-model="dialogOptions.wechat"
        placeholder="请输入微信账号"></ElInput>
    </ElFormItem>
    <template #footer>
      <ElButton @click="dialogOptions.visible = false">取消</ElButton>
      <ElButton
        type="primary"
        :loading="dialogOptions.loading"
        @click="dialogOptions.confirm">
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
  import { getWeChatList, updateWeChatAccount } from '@/apis/wechat'
  import { KeywordSearchInput, TablePageLayout } from '@/components'
  import { useDict } from '@/stores/dict'
  const { TALENT_WECHAT_STATUS } = useDict('TALENT_WECHAT_STATUS')
  const searchData = reactive({
    talentBid: '',
    name: '',
    status: '',
    claimWxwork: '',
    belongAccount: '',
  })

  // 查询
  function handleSearch() {
    paginationData.pageNum = 1
    getTableData()
  }

  const searchFormRef = useTemplateRef('searchFormRef')
  // 重置
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  const tableData = ref([])
  const tableLoading = ref(false)
  function getTableData() {
    tableLoading.value = true
    getWeChatList({
      ...searchData,
      pageSize: paginationData.pageSize,
      pageNum: paginationData.pageNum,
    })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page
      })
      .finally(() => {
        tableLoading.value = false
      })
  }

  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
  // 改变每页条数
  function handlePageOrSizeChange(isResize) {
    if (isResize) paginationData.pageNum = 1
    handleSearch()
  }

  // dialog
  const dialogOptions = reactive({
    visible: false,
    loading: false,
    bid: undefined,
    wechat: '',
    confirm: async () => {
      try {
        if (!dialogOptions.wechat) {
          ElMessage.warning('请输入微信账号')
          return
        }
        dialogOptions.loading = true
        await updateWeChatAccount({
          bid: dialogOptions.bid,
          wechat: dialogOptions.wechat,
        })
        ElMessage.success('微信账号已更新')
        getTableData()
        dialogOptions.visible = false
      } catch (error) {
        console.log(error)
      } finally {
        dialogOptions.loading = false
      }
    },
    close: () => {
      dialogOptions.wechat = ''
      dialogOptions.id = undefined
      dialogOptions.loading = false
    },
    edit: (row) => {
      dialogOptions.bid = row.bid
      dialogOptions.wechat = row.wechat
    },
  })
  onMounted(() => {
    getTableData()
  })
</script>

<style lang="scss" scoped></style>
