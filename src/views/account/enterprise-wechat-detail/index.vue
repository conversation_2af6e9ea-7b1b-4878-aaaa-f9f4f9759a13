<template>
  <TablePageLayout>
    <template #header>
      <PageHeader content="查看职位"></PageHeader>
    </template>
    <template #search>
      <div class="detail_content">
        <!-- 企微账号信息 -->
        <div class="account_content">
          <div class="account_content_header">
            <div class="info">
              <div
                class="flex h-[52px] w-[52px] items-center justify-center rounded-[12px] bg-[#fff]">
                <img
                  class="info_img"
                  src="@/assets/images/wechat/logo.png" />
              </div>
              <div class="flex flex-col justify-between">
                <div class="flex h-[28px] items-center gap-[6px]">
                  <span class="text-[20px] font-bold">{{ detail.username }}</span>
                  <ElTag
                    type="success"
                    round>
                    {{
                      detail.accountStatus === 0
                        ? '待验证'
                        : detail.sumBindNumber === 5000
                          ? '满员'
                          : '正常'
                    }}
                  </ElTag>
                </div>
                <div class="flex h-[22px] items-center">
                  <ElBreadcrumb>
                    <ElBreadcrumbItem>
                      <p class="text-[#606266]">{{ detail.companyName }}</p>
                    </ElBreadcrumbItem>
                    <ElBreadcrumbItem>
                      <div class="flex gap-[6px]">
                        <span class="text-[#A8ABB2]">手机号 :</span>
                        <p class="text-[#606266]">{{ detail.phone }}</p>
                      </div>
                    </ElBreadcrumbItem>
                    <ElBreadcrumbItem>
                      <div class="flex gap-[6px]">
                        <span class="text-[#A8ABB2]">登录状态 :</span>
                        <p class="text-[#606266]">
                          {{ detail.loginStatus === 0 ? '未登录' : '已登录' }}
                        </p>
                      </div>
                    </ElBreadcrumbItem>
                  </ElBreadcrumb>
                </div>
              </div>
            </div>
            <ElButton
              type="primary"
              :style="{
                backgroundColor: '#ECF5FF',
                color: '#409EFF',
                borderColor: '#9FCEFF',
              }">
              <template #icon>
                <i class="iconfont icon-ScanOutlined"></i>
              </template>
              扫码登录
            </ElButton>
          </div>
          <!-- 绑定人才数 -->
          <div class="bind_content">
            <div class="bind_content_item">
              <div class="bind_content_item_left">人才绑定总数</div>
              <ElBreadcrumb separator="/">
                <ElBreadcrumbItem>
                  <div class="flex items-center">
                    <p class="text-[#A8ABB2]">
                      <span class="mr-[6px] text-[20px] font-bold text-[#000] italic">
                        {{ addCommasToNumber(detail.sumBindNumber) }}
                      </span>
                      (已绑定)
                    </p>
                  </div>
                </ElBreadcrumbItem>
                <ElBreadcrumbItem>
                  <div class="flex items-center">
                    <p class="text-[#A8ABB2]">
                      <span class="mr-[6px] text-[20px] font-bold italic">
                        {{ addCommasToNumber(5000) }}
                      </span>
                      (总量)
                    </p>
                  </div>
                </ElBreadcrumbItem>
              </ElBreadcrumb>
            </div>
            <div class="bind_content_item">
              <div class="bind_content_item_left">今日绑定人才数</div>
              <ElBreadcrumb separator="/">
                <ElBreadcrumbItem>
                  <div class="flex items-center">
                    <p class="text-[#A8ABB2]">
                      <span class="mr-[6px] text-[20px] font-bold text-[#000] italic">
                        {{ addCommasToNumber(detail.todayBindNumber) }}
                      </span>
                      (已绑定)
                    </p>
                  </div>
                </ElBreadcrumbItem>
                <ElBreadcrumbItem>
                  <div class="flex items-center">
                    <p class="text-[#A8ABB2]">
                      <span class="mr-[6px] text-[20px] font-bold italic">
                        {{ addCommasToNumber(300) }}
                      </span>
                      (总量)
                    </p>
                  </div>
                </ElBreadcrumbItem>
              </ElBreadcrumb>
            </div>
          </div>
        </div>
      </div>
      <ElDivider />
      <div class="flex w-full items-center justify-between">
        <ElForm
          ref="formRef"
          class="flex-1"
          :model="form">
          <ElRow :gutter="16">
            <ElCol :span="4">
              <ElFormItem label="绑定日期">
                <ElDatePicker
                  v-model="form.date"
                  placeholder="请选择一个日期" />
              </ElFormItem>
            </ElCol>
            <ElButton
              type="primary"
              @click="onSearch">
              查询
            </ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElRow>
        </ElForm>
        <ElButton style="color: #c0c4cc">
          <template #icon>
            <i class="iconfont icon-refresh-left text-[#C0C4CC]"></i>
          </template>
          一键拉取客户列表
        </ElButton>
      </div>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="loading"
        :max-height="maxHeight"
        :data="tableData"
        :border="true">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无人才微信账号" />
        </template>
        <ElTableColumn
          label="人才ID"
          prop="talentBid" />
        <ElTableColumn
          label="人才名称"
          prop="name" />
        <ElTableColumn
          label="微信账号"
          prop="wechat" />
        <ElTableColumn
          label="状态"
          prop="status">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  待绑定: 'warning',
                  待通过: 'primary',
                  绑定失败: 'danger',
                  绑定成功: 'success',
                  被单删: 'info',
                }[row.status]
              ">
              {{ row.status }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="绑定时间"
          prop="createdAt"></ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageOrSizeChange(true)"
        @current-change="handlePageOrSizeChange(false)" />
    </template>
  </TablePageLayout>
  <LoginWechat
    v-model="dialogVisible"
    :loading="dialogLoading"
    @over="filled_over"></LoginWechat>
</template>

<script setup>
  import { getWeChatWorkDetail, getWeChatWorkListByBid } from '@/apis/wechat'
  import { PageHeader, TablePageLayout } from '@/components'
  import { addCommasToNumber } from '@/utils/math'
  import LoginWechat from '../components/login-wechat.vue'
  import { useLoginDialog } from '../hooks'
  const route = useRoute()
  const router = useRouter()
  //  ID
  const bid = computed(() => {
    return route.query.bid || ''
  })
  const form = reactive({
    date: '',
  })
  const formRef = useTemplateRef('formRef')
  // 重置
  function handleReset() {
    formRef.value.resetFields()
  }

  const detail = ref(null)
  const tableData = ref([])
  const loading = ref(false)
  function onSearch() {
    if (!import.meta.env.DEV && !bid.value) {
      ElMessage.error('账号异常，请重新选择')
      router.back()
      return
    }
    loading.value = true
    getWeChatWorkDetail({
      bid: bid.value,
    })
      .then((data) => {
        detail.value = data
      })
      .finally(() => {
        loading.value = false
      })
  }
  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
  // 改变每页条数
  function handlePageOrSizeChange(isResize) {
    if (isResize) paginationData.pageNum = 1
    getTableData()
  }
  function getTableData() {
    loading.value = true
    getWeChatWorkListByBid({
      bid: bid.value,
      pageSize: paginationData.pageSize,
      pageNum: paginationData.pageNum,
    })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page
      })
      .finally(() => {
        loading.value = false
      })
  }
  const { dialogVisible, dialogLoading, filled_over } = useLoginDialog()
  onMounted(() => {
    onSearch()
    getTableData()
  })
</script>

<style lang="scss" scoped>
  .detail {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;

    &_content {
      flex: 1;
      margin-top: 20px;
      overflow: auto;

      .account_content {
        padding: 12px 22px 22px;
        border-radius: 8px;
        background-color: #fafcff;
        &_header {
          display: flex;
          justify-content: space-between;
          .info {
            display: flex;
            height: 52px;
            gap: 12px;
            color: #333333;
            &_img {
              width: 32px;
              height: 32px;
            }
          }
        }
        .bind_content {
          display: flex;
          justify-content: space-between;
          height: 48px;
          margin-top: 12px;
          gap: 16px;

          &_item {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 10px;
            border-radius: 6px;
            background-color: #fff;

            &_left {
              height: 28px;
              color: #606266;
              font-size: 14px;
              line-height: 28px;
            }
          }
        }
      }
    }
  }
</style>
