<template>
  <ElConfigProvider :locale="zhCn">
    <RouterView />
    <!-- <LargeScreen v-if="show" /> -->
  </ElConfigProvider>
</template>

<script setup>
  // import LargeScreen from '@/components/large-screen/index.vue'
  import { useDictionaryStore } from '@/stores/dictionary'
  // import { useLargeScreenStore } from '@/stores/large-screen.js'
  import { getToken } from '@/utils/config'
  import zhCn from 'element-plus/es/locale/lang/zh-cn'
  // import screenfull from 'screenfull'

  // const largeScreenStore = useLargeScreenStore()
  // const { show } = storeToRefs(largeScreenStore)
  const dictionaryStore = useDictionaryStore()

  watchEffect(() => {
    if (getToken()) {
      dictionaryStore.init()
    }
    // screenfull.on('change', () => {
    // if (!screenfull.isFullscreen) {
    // largeScreenStore.setShow()
    // }
    // })
  })
</script>

<style scoped lang="scss"></style>
